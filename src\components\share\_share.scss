.share {
    $this: &;

    &.is-open {
        #{$this}__block {
            transform: translateX(-50%) scale(1);

            @include breakpoint(medium down) {
                transform: translateX(-75%) scale(1);
            }
        }

        #{$this}__toggle {
            background-color: var(--color-1--1);
        }

        #{$this}__toggle-icon {
            color: $color-white;
        }

        .tools & {
            #{$this}__toggle {
                background-color: transparent;
            }

            #{$this}__toggle-icon {
                color: var(--color-1--1);
            }
        }
    }

    &__toggle {
        @include trs;
        @include min-size(40px);
        background-color: var(--color-1--5);
        border: 1px solid var(--color-1--5);
        border-radius: 50%;
        color: $color-white;
        font-size: 1.8rem;
        padding: 0;
        position: relative;
        z-index: 6;

        @include on-event {
            background-color: $color-white;
            color: var(--color-1--1);
        }

        .tools & {
            @include breakpoint(small down) {
                @include min-size(28px);
                background-color: transparent;
                border-color: transparent;
                color: var(--color-1--1);
                font-size: 1.6rem;

                @include on-event {
                    background-color: var(--color-1--1);
                    border-color: var(--color-1--1);
                    color: $color-white;
                }
            }
        }
    }

    &__toggle-icon {
        display: inline-block;
        font-size: 1em;
        pointer-events: none;

        &::before {
            @extend %icons-font-aliasing;
            content: fa-content($fa-var-share-alt);
        }
    }

    &__toggle-text {
        @extend %visually-hidden;
    }

    &__block {
        @include absolute(100%, null, null, 50%);
        background-color: var(--color-1--1);
        border-radius: 5px;
        margin-top: 15px;
        padding: 10px 20px;
        transform: translateX(-50%) scale(0);
        transform-origin: 50% 0;

        &::before {
            content: '';
            @include absolute(null, null, 100%, 50%);
            @include triangle('top', var(--color-1--1), 20px, 10px);
            transform: translateX(-50%);

            @include breakpoint(medium down) {
                transform: translateX(100%);
            }
        }
    }
}

.share-list {
    &__link {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        background-color: transparent;
        border: 0;
        color: $color-white;
        cursor: pointer;
        display: block;
        padding-left: 25px;
        position: relative;
        text-decoration: none;

        @include fa-icon-style(false) {
            @include absolute(50%, null, null, 0);
            transform: translateY(-50%);
        }

        @include on-event {
            text-decoration: underline;
        }
    }

    &__link-text {
        pointer-events: none;
    }
}
