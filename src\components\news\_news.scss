// News section global
.news-content,
.news-widget {
    .news-list {
        &__item {
            @include breakpoint(large) {
                margin-bottom: 15px;
            }
        }
    }
}

.news-home {
    width: 50%;

    @include breakpoint(small down) {
        width: 100%;
    }

    &.section {
        margin-bottom: 64px;
        margin-top: 0;

        @include breakpoint(small down) {
            padding-inline: 17px;
        }
    }

    &__container {
        @extend %container;
        padding-inline: 0;
        padding-top: 66px;
        position: relative;

        @include breakpoint(medium down) {
            padding-left: 10px;
            padding-top: 28px;
        }

        @include breakpoint(small down) {
            padding-left: 0;
            padding-top: 45px;
        }
    }

    &__title {
        margin-bottom: 44px;

        @include breakpoint(small down) {
            margin-bottom: 40px;
        }
    }

    &__content {
        width: 573px;

        @include breakpoint(medium down) {
            padding: 0;
            width: 320px;
        }
    }

    &__more-links {
        @include breakpoint(medium down) {
            margin-top: 29px;
        }

        @include breakpoint(small down) {
            margin-top: 29px;
            text-align: center;
        }

        .btn.is-primary {
            background-color: transparent;
            border: 0;
            color: var(--color-1--1);
            font-size: 1.2rem;
            margin-left: 6px;
            min-height: auto;
            padding: 0;

            @include breakpoint(medium down) {
                margin-left: 3px;
            }

            @include on-event() {
                .btn__text {
                    text-decoration: underline;
                }
            }
        }
    }
}

.news-review-home {
    display: flex;
    margin-inline: auto;
    max-width: 1320px;
    padding-inline: 20px;
    position: relative;

    @include breakpoint(medium down) {
        max-width: 768px;
    }

    @include breakpoint(small down) {
        flex-direction: column;
        max-width: 360px;
        padding-inline: 0;
    }

    &::before {
        @include size(100%);
        @include absolute(0, 0, null, 50%);
        background-color: $color-3--7;
        content: "";
        z-index: -1;

        @include breakpoint(small down) {
            content: none;
        }
    }

    .news-list {
        justify-content: flex-start !important;
        margin: 0;

        &__item {
            margin-bottom: 32px;
            max-width: 282px;
            padding: 0;

            &:first-of-type {
                @include breakpoint(large only) {
                    margin-right: 9px;
                }
            }

            &:last-of-type {
                @include breakpoint(medium down) {
                    margin-bottom: 0;
                }
            }

            @include breakpoint(medium down) {
                margin: 0 0 30px;
                max-width: none;
            }
        }
    }
}
