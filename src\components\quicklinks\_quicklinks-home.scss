.quicklinks-home {
    $this: &;

    &__side {
        width: 50%;

        @include breakpoint(small down) {
            width: 100%;
        }

        &:first-of-type {
            @include media-max(1300) {
                padding-left: 20px;
            }

            @include breakpoint(medium down) {
                padding-left: 36px;
            }

            @include breakpoint(small down) {
                position: relative;

                &::before {
                    @include size(1000vw, calc(100% + 76px));
                    @include absolute(-42px, null, null, 50%);
                    background-color: $color-3--7;
                    content: "";
                    transform: translate(-50%);
                    z-index: -1;
                }
            }
        }

        &:last-of-type {
            @include breakpoint(large only) {
                padding-left: 52px;
            }

            @include breakpoint(small down) {
                margin-top: 90px;
            }

            .quicklinks-home-side__title {
                @include breakpoint(medium down) {
                    margin-left: 41px;
                }

                @include breakpoint(small down) {
                    margin-bottom: 18px;
                    margin-left: 17px;
                }

                span {
                    @include breakpoint(large only) {
                        margin-bottom: 10px;
                        margin-top: 0;
                    }
                }
            }
        }

        @include breakpoint(medium down) {
            padding-left: 36px;
            padding-right: 61px;
        }

        @include breakpoint(small down) {
            padding-right: 36px;
        }

        .quicklinks-home-side {
            &__title {
                @include font(var(--typo-1), 2.6rem, var(--fw-normal));
                display: flex;
                line-height: 30px;
                margin-bottom: 18px;

                @include breakpoint(medium down) {
                    margin-bottom: 18px;
                    margin-left: 32px;
                }

                @include breakpoint(small down) {
                    justify-content: center;
                    margin-bottom: 22px;
                    margin-inline: auto;
                }

                span {
                    margin-top: 10px;

                    @include breakpoint(large only) {
                        width: 139px;
                    }
                }

                svg {
                    @include size(53px, 65px);
                    fill: var(--color-2--4);
                    margin-right: 5px;

                    @include breakpoint(medium down) {
                        @include size(40px, 49px);
                        margin-right: 10px;
                    }
                }
            }

            &__quicklinks {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
            }

            &__quicklinks-item {
                border: 1px solid $color-3--6;
                width: 140px;
            }

            &__quicklinks-link {
                @include trs;
                @include font(var(--typo-1), 1.6rem, var(--fw-normal));
                @include size(100%);
                align-items: center;
                cursor: pointer;
                display: flex;
                flex-direction: row;
                justify-content: center;
                line-height: 20px;
                margin: 0 auto;
                padding: 18px 7px;
                text-align: center;
                text-decoration: none;

                &:nth-of-type(even) {
                    @include breakpoint(medium down) {
                        margin-right: 0;
                    }
                }

                @include on-event() {
                    background-color: var(--color-1--1);
                    border-color: var(--color-1--1);
                    color: $color-white;
                }
            }
        }
    }

    &.section {
        margin-bottom: 0;
        margin-top: 0;
        padding-bottom: 70px;
        padding-top: 52px;
        position: relative;

        @include breakpoint(medium down) {
            margin-inline: auto;
            max-width: 768px;
            padding-bottom: 48px;
            padding-top: 32px;
        }

        @include breakpoint(small down) {
            margin-top: 22px;
            max-width: 360px;
            padding-bottom: 0;
            padding-top: 46px;
        }

        &::before {
            @include size(100%);
            @include absolute(0, 50%, null, null);
            background-color: $color-3--7;
            content: "";
            z-index: -1;

            @include breakpoint(small down) {
                content: none;
            }
        }

        + #{$this} {
            margin-top: -114px;
            position: relative;

            @include breakpoint(medium down) {
                margin-top: -94px;
            }

            @include breakpoint(small down) {
                margin-top: -43px;
            }
        }

        .home-hospital-page & {
            margin: 0 0 150px;

            @include breakpoint(medium down) {
                margin: 0 0 100px;
            }

            @include breakpoint(small down) {
                margin: 0 0 80px;
            }

            .quicklinks {
                padding-bottom: 0;
                padding-top: 0;
            }
        }
    }

    &__container {
        @extend %container;
        padding-inline: 0;

        @include breakpoint(small down) {
            max-width: 520px;
        }
    }

    .section__content {
        display: flex;

        @include breakpoint(small down) {
            flex-direction: column;
        }
    }

    .quicklinks {
        background-color: var(--color-1--2);
        padding: 60px 90px;
        width: 100%;

        @include breakpoint(medium down) {
            padding: 45px 30px;
        }

        @include breakpoint(small down) {
            padding: 19px 30px;
        }
    }

    .quicklinks-block {
        &__control {
            @include breakpoint(medium only) {
                font-size: 1.6rem;
            }
        }
    }

    .quicklink-item {
        &__svg-wrapper {
            @include breakpoint(medium only) {
                @include size(50px);
            }
        }

        &__text {
            color: var(--color-1--2);

            @include breakpoint(medium only) {
                font-size: 1.1rem;
                line-height: 1;
            }

            .underline {
                @include multiline-underline();
            }
        }
    }

    &.is-fluid {
        @include breakpoint(medium) {
            background: linear-gradient(90deg, var(--color-1--2) 0%, var(--color-1--2) 50%, var(--color-1--1) 50%, var(--color-1--1) 100%);
        }

        .quicklinks-home__container {
            @include breakpoint(small down) {
                max-width: initial;
            }
        }

        .section__content {
            @include breakpoint(small down) {
                margin-left: -20px;
                margin-right: -20px;
            }
        }

        .quicklinks {
            box-shadow: none;
        }
    }

    &.has-info-block {
        .quicklinks {
            width: calc(100% - 384px);

            @include breakpoint(medium down) {
                width: calc(100% - 256px);
            }

            @include breakpoint(small down) {
                width: 100%;
            }
        }
    }

    &.is-light {
        .quicklinks {
            background-color: $color-white;
        }
    }

    &.has-box-shadow,
    .has-box-shadow {
        box-shadow: 0 0 50px rgba($color-black, 0.16);
    }
}

.hero {
    &.is-type-1 {
        + .quicklinks-home {
            margin-top: -114px;
            position: relative;

            @include breakpoint(medium down) {
                margin-top: -94px;
            }

            @include breakpoint(small down) {
                margin-top: -43px;
            }
        }
    }
}
