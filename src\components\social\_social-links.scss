.social-links {
    $this: &;

    color: var(--color-1--1);
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin: -1px 0 0 7px !important;
    padding: 0;

    @include breakpoint(medium down) {
        margin: 8px 0 0 102px !important;
    }

    @include breakpoint(small down) {
        margin-inline: auto !important;
    }

    @include breakpoint(small down) {
        justify-content: center;
    }

    &__item:not(:last-child) {
        margin-right: 6px;
    }

    &__link {
        @extend %button;
        @extend %button-size-small;
        @extend %button-style-only-icon;
        @extend %button-style-circle;
        @include size(40px);
        background-color: $color-white;
        border-color: $color-white;
        font-size: 1.8rem;
        min-height: 40px;
        
        @include on-event() {
            background-color: var(--color-2--3);
            border-color: var(--color-2--3);
            color: var(--color-1--2);
        }
    }

    &.is-floating {
        flex-direction: column;

        #{$this}__item {
            border: 0;
            margin: 0 0 2px;
        }

        #{$this}__link {
            @include size(36px);
            background-color: lighten($color-3--2, 12.55);
            color: $color-3--3;
            font-size: 1.4rem;

            @include on-event {
                background-color: $color-3--3;
                color: $color-white;
            }
        }
    }

    &.is-inverted {
        #{$this}__item:not(:last-child) {
            margin-right: 5px;
        }

        #{$this}__link {
            @include size(40px);
            background-color: $color-white;
            color: var(--color-1--1);
            font-size: 1.8rem;

            @include on-event {
                background-color: var(--color-1--3);
                color: var(--color-1--2);
            }
        }
    }

    &.is-large {
        #{$this}__item:not(:last-child) {
            @include breakpoint(large only) {
                margin-right: 10px;
            }
        }

        #{$this}__link {
            @include size(78px);
            font-size: 2.2rem;

            @include breakpoint(medium only) {
                @include size(65px);
            }

            @include breakpoint(small down) {
                @include size(40px);
                font-size: 1.8rem;
            }
        }
    }
}
