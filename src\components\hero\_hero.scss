.hero {
    &__container {
        @extend %container;
        @extend %container-fluid;
        align-items: center;
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
        margin-inline: auto;
        max-width: 1320px;
        padding-inline: 20px;

        @include breakpoint(medium down) {
            align-items: flex-start;
            max-width: 768px;
            padding: 0;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            max-width: 100%;
        }
    }
}
