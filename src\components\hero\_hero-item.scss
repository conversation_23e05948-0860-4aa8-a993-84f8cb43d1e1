@mixin gradient($useTop: true, $useBottom: true) {
    @if $useTop {
        &::before {
            @include absolute(0, 0);
            @include size(100%, 310px);
            background-image: linear-gradient(to top, rgba($color-black, 0) 0%, rgba($color-black, 0.52) 64%, rgba($color-black, 0.6) 100%);
            content: "";
            pointer-events: none;
            z-index: 1;

            @include breakpoint(medium down) {
                height: 226px;
            }

            @include breakpoint(small down) {
                height: 120px;
            }
        }
    }

    @if $useBottom {
        &::after {
            @include absolute(null, 0, 0);
            @include size(100%, 481px);
            background-image: linear-gradient(180deg, rgba($color-black, 0) 0%, rgba($color-black, 0.52) 64%, rgba($color-black, 0.6) 100%);
            content: "";
            pointer-events: none;

            @include breakpoint(medium down) {
                height: 334px;
            }

            @include breakpoint(small down) {
                height: 247px;
            }
        }
    }
}

.hero-item {
    $this: &;

    position: relative;

    .swiper-slide-active &,
    .has-video & {
        &__video {
            opacity: 1;
            visibility: visible;
        }

        @include breakpoint(medium down) {
            margin-left: auto;
        }
    }

    &.swiper-slide:not(.swiper-slide-active) {
        z-index: -1;
    }

    &__media {
        height: 100%;
        overflow: hidden;
        position: relative;

        .has-video & {
            @include responsive-ratio(1920, 1080, "before");
            @include size(826px, 434px);
            margin-top: -80px;
            position: relative;
            z-index: 99;

            @include breakpoint(medium down) {
                @include responsive-ratio(768, 620, "before");
                @include size(508px, 267px);
                margin-left: auto;
                margin-top: 0;
            }

            @include breakpoint(small down) {
                @include responsive-ratio(360, 475, "before");
                @include size(100vw, 189px);
            }
        }
    }

    &__content {
        .has-video & {
            z-index: 100 !important;
        }
    }

    &__link {
        display: block;
        height: 100%;

        .lazy.lazyloaded:not(.is-no-image) {
            animation: none !important;
        }

        &:hover {
            @at-root {
                a#{&} {
                    img {
                        transform: scale(1.2);
                    }
                }
            }
        }

        &:focus,
        &:focus-within {
            #{$this}__picture {
                outline: 4px solid var(--color-1--1);
                outline-offset: -6px;
            }

            img {
                transform: none;
            }
        }
    }

    &__picture {
        @include trs;
        display: block;
        height: 100%;
        position: relative;

        img {
            @include trs;
            @include object-fit;
            @include size(100%);
        }
    }

    &__video {
        @include trs;
        @include abs-center;
        @include size(100%);
        opacity: 0;
        overflow: hidden;
        visibility: hidden;

        .video {

            background-color: $color-black;
            height: 100%;
            position: relative;
            @include on-event {
                .video-controls__action {
                    opacity: 1;
                }
            }

            &__video-wrap {
                display: block;
                height: 100%;
                overflow: hidden;
            }

            &__controls-wrap {
                @include absolute(50%, null, null, 50%);
                transform: translate(-50%, -50%);
                z-index: 2;
            }

            .video-controls {
                &__action {
                    @include trs;
                    @include size(50px);
                    background-color: var(--color-1--1);
                    border: 0;
                    border-radius: 50%;
                    color: rgba($color-white, 0.8);
                    cursor: pointer;
                    display: block;
                    opacity: 0;
                    padding: 0;
                    z-index: 1;

                    &:focus {
                        opacity: 1;
                    }
                }

                &__icon {
                    pointer-events: none;
                }
            }
        }

        video {
            @include object-fit;
            @include absolute(0, 0, 0, 0);
            @include size(100%);
            margin: auto;
        }
    }

    &__title {
        display: block;
    }

    &__search {
        @include absolute(null, null, 190px, 50%);
        transform: translateX(-50%);
        width: 574px;
        z-index: 5;

        @include breakpoint(medium down) {
            bottom: 140px;
        }

        @include breakpoint(small down) {
            bottom: 70px;
            max-width: 574px;
            padding: 0 20px;
            width: 100%;
        }
    }

    &__keywords[class] {
        margin-top: 25px;

        @include breakpoint(medium down) {
            margin-top: 20px;
        }

        @include breakpoint(medium down) {
            margin-top: 15px;
        }
    }
}
