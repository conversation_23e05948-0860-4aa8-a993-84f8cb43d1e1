.search {
    &__toggle {
        @extend %button;
        @extend %button-style-only-icon;
        @include trs;
        @include size(24px, 25px);
        background-color: transparent;
        border: 0;
        border-radius: 0;
        min-height: auto;

        @include on-event {
            @include fa-icon-style(false) {
                animation: wiggle-animation forwards 0.8s;
            }
        }

        @include fa-icon-style(false) {
            color: $color-white;
            font-size: 2rem;
        }
    }
}

@keyframes wiggle-animation {
    from {
        transform: rotate(0);
    }

    25% {
        transform: rotate(30deg);
    }

    60% {
        transform: rotate(-30deg);
    }

    100% {
        transform: rotate(0);
    }
}
