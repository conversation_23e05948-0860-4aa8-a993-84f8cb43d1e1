.main-nav-toggle {
    $this: &;

    @include size(20px, 21px);
    @include trs;
    align-content: center;
    align-items: center;
    background-color: transparent;
    border: 0;
    color: $color-white;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 0;
    position: relative;

    @include on-event {
        @include breakpoint(large only) {
            #{$this}__text {
                font-size: 1.2rem;
            }

            #{$this}__bar {
                &:nth-child(1) {
                    top: -3px;
                }

                &:nth-child(2) {
                    opacity: 0;
                }

                &:nth-child(3) {
                    top: 23px;
                }
            }
        }
    }

    &__bars {
        @include size(100%);
        position: relative;
    }

    &__bar {
        @include trs;
        @include size(20px, 2px);
        @include absolute(50%, null, null, 50%);
        background-color: $color-white;
        transform: translateX(-50%);

        &:nth-child(1) {
            top: 3px;
        }

        &:nth-child(2) {
            opacity: 1;
            top: 9px;
        }

        &:nth-child(3) {
            top: 15px;
        }
    }

    &__text {
        @include trs;
        color: inherit;
        font-size: 0;
        font-weight: var(--fw-normal);
        position: absolute;
    }

    &.is-open {
        @include breakpoint(large only) {
            #{$this}__bar {
                left: 0;

                &,
                &:first-child,
                &:last-child {
                    opacity: 0;
                    top: 50%;
                }

                &:first-child {
                    opacity: 1;
                    transform: translateY(-50%) rotate(45deg);
                }

                &:last-child {
                    opacity: 1;
                    transform: translateY(-50%) rotate(-45deg);
                }
            }

            #{$this}__text {
                opacity: 0;
            }
        }
    }
}
