.hero-carousel {
    @include size(826px, 434px);
    $this: &;
    margin-top: -80px;
    position: relative;
    z-index: 99;

    @include breakpoint(medium down) {
        @include size(508px, 267px);
        margin-left: auto;
        margin-top: 0;
    }

    @include breakpoint(small down) {
        @include size(100%, 189px);
    }

    &__wrapper {
        display: flex;
    }

    &__control {
        @include absolute(auto !important, null, 12px, null);
        @include size(50px);
        background: none;
        border: none;
        color: $color-white;
        cursor: pointer;
        overflow: hidden;
        padding: 0;
        transform: translateY(-50%);
        z-index: 99999999;

        @include breakpoint(medium down) {
            bottom: 62px;
        }

        @include breakpoint(small down) {
            bottom: 75px;
        }

        @include fa-icon-style(false) {
            color: inherit;
            font-size: 3rem;
            font-weight: var(--fw-bold);
            vertical-align: middle;
        }

        @include on-event {
            background-color: var(--color-1--1);
        }

        &.is-prev {
            right: 230px;

            @include breakpoint(medium down) {
                right: 100px;
            }
        }

        &.is-next {
            right: 190px;

            @include breakpoint(medium down) {
                right: 45px;
            }
        }

        &.swiper-button-disabled {
            opacity: 0.3;
        }
    }

    &__container {
        height: 100%;

        &.swiper-container {
            overflow: visible;
        }
    }

    .swiper-pagination {
        align-items: center;
        display: inline-flex;
        margin: 0;
        width: auto;
        z-index: 10;

        &__bullet {
            @include trs(opacity);
            display: block;
            padding: 4px 3px;

            &.is-active {
                .swiper-pagination__bullet-btn {
                    background-color: var(--color-2--1);
                    border-color: var(--color-2--1);
                    width: 28px;
                }
            }

            &:not(.is-active) {
                @include on-event {
                    opacity: 0.7;
                }
            }
        }

        &__bullet-btn {
            @include trs;
            @include size(10px);
            background-color: transparent;
            border: 1px solid $color-white;
            border-radius: 5px;
            cursor: pointer;
            display: block;
            padding: 0;
            position: relative;

            &::before {
                @include absolute(-6px, null, null, -4px);
                @include size(calc(100% + 8px), calc(100% + 12px));
                content: '';
            }
        }
    }

    &__actions {
        display: inline-flex;
        padding: 1px 2px 0;
    }

    & &__action {
        @include trs(opacity);
        background: none;
        border: 0;
        color: $color-white;
        cursor: pointer;
        display: inline-block;
        font-size: 1.1rem;
        padding: 0 5px;

        @include on-event {
            opacity: 0.7;
        }

        &.is-active {
            color: var(--color-2--1);
            display: none;
        }

        span {
            pointer-events: none;

            &::before {
                font-weight: var(--fw-bold);
            }
        }
    }

    &__group {
        @include absolute(null, 10px, 30px, null);
        align-items: center;
        display: flex;
        flex-direction: row-reverse;
        transform: translateX(-50%);
        z-index: 10000000;

        @include breakpoint(small down) {
            bottom: 60px;
        }

        &.has-shadow {
            &::before {
                @include absolute(50%, null, null, 50%);
                @include size(calc(100% + 100px), 80px);
                background: radial-gradient(closest-side at 50% 50%, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.52) 24%, rgba(0, 0, 0, 0.15) 71%, rgba(0, 0, 0, 0) 100%) 0 0 no-repeat padding-box;
                content: '';
                pointer-events: none;
                transform: translate(-50%, -50%);
                z-index: -1;
            }
        }
    }
}
