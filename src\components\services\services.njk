{%- from 'views/utils/constants.njk' import kGlobalLinks -%}
{%- from 'views/core-components/infos.njk' import InfoItem, CreateInfoItem -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/widget.njk' import Widget -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'components/contacts/contacts.njk' import ContactsItem -%}

{#
    ServicesItem template.
    @param {object} settings - user settings object
#}
{%- macro ServicesItem( imageSizes = ['200x133?1279', '250x166'] ) -%}
    <article class="services-item">
        <div class="services-item__wrapper">
            {{ Image({
                className: 'services-item__picture',
                sizes: imageSizes,
                type: 'no-image' if (range(5, 20) | random) > 15 else 'default',
                serviceID: range(50) | random
            }) }}
            <div class="services-item__content">
                <h3 class="services-item__title">
                    <a href="single-services.html" class="services-item__title-link">
                        <span class="underline">{{ lorem(range(4, 20) | random, 'words') | capitalize }}</span>
                    </a>
                </h3>
                <p class="services-item__name">
                    <span class="services-item__name-title">Docteur</span>
                    <span class="services-item__name-first">Laurent</span>
                    <span class="services-item__name-last">Duisfugiat</span>
                    <span class="services-item__name-position">Chef de service</span>
                </p>
            </div>
        </div>
        <div class="services-item__info">
            <h4 class="services-item__info-title">Maladies traitées</h4>
            <p class="services-item__info-teaser">{{ lorem(2) }}</p>
        </div>
    </article>
{%- endmacro -%}

{#
    ServicesList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} smCols - mobile columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro ServicesList(
    itemClass = 'has-mb-1',
    count = 6,
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1
    ) -%}
    {% call List(
        listClass = 'list-services',
        itemClass = itemClass,
        count = count,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols
        ) %}
        {{ ServicesItem() }}
    {% endcall %}
{%- endmacro -%}

{#
    ServicesContactsItem template.
#}

{%- macro ServicesContacts(
    items = [
        'Secrétariat Adultes, explorations, paupières',
        'Secrétariat de l\'aide médicale à la procréation (AMP)'
        ]
    ) -%}
    {% set titleId = range(100) | random %}
    <section class="services-contact">
        <h2 class="sr-only">Coordonnées du service</h2>
        <div class="services-contact__top">
            <ul class="services-contact__list">
                {% for text in items %}
                    <li class="services-contact__listitem">
                        <p class="services-contact__listitem-title">
                            <span>{{text}}</span>
                        </p>
                        <div class="services-contact__listitem-link">
                            {{ Link(
                                href = 'tel:0465715233',
                                text = '04 65 71 52 33',
                                textSrOnly = 'Téléphone',
                                className = 'btn is-small',
                                icon = 'far fa-phone'
                            ) }}
                            {% if loop.index == 1 %}
                            {{ Link(
                                href = 'tel:0465715233',
                                text = '04 65 71 52 33',
                                textSrOnly = 'Téléphone',
                                className = 'btn is-small',
                                icon = 'far fa-fax'
                            ) }}
                            {% endif %}
                            {{ Link(
                                href = 'mailto:<EMAIL>',
                                text = 'Courriel',
                                className = 'btn is-small',
                                icon = 'far fa-at',
                                attrs = {
                                    'aria-describedby': 'services-contact-name-' + titleId
                                }
                            ) }}
                        </div>
                    </li>
                {% endfor %}
            </ul>
        </div>
        <div class="services-contact__bottom">
            <h3 class="services-contact__title">L'équipe médicale</h3>
            {{DoctorItem()}}
            {# <div class="services-contact__wrapper">
                <div class="services-contact__name-wrapper">
                    <h3 class="services-contact__name" id="services-contact-name-{{ titleId }}">
                        <span class="services-contact__name-title">Dr</span>
                        <span class="services-contact__name-first">Laurent</span>
                        <span class="services-contact__name-last">Duisfugiat</span>
                        <span class="services-contact__name-position">Chef de service</span>
                        <span class="services-contact__name-specialite">Spécialité : Biochimie générale</span>
                    </h3>
                    {%- call Link(
                        href = 'mailto:<EMAIL>',
                        className = 'btn is-small',
                        icon = 'far fa-at',
                        attrs = {
                            'aria-describedby': 'services-contact-name-' + titleId
                        }
                    ) -%}
                        <span class="btn__text">ME CONTACTER</span>
                        <span class="sr-only">Par courriel</span>
                    {%- endcall -%}
                </div>
                {{ Image({
                    className: 'services-contact__picture',
                    sizes: imageSizes,
                    serviceID: range(50) | random
                }) }}
            </div> #}

            <div class="medical-team">
                {% for item in range(6)%}
                    {{DoctorItem(position = false)}}
                {% endfor %}
            </div>
        </div>
        
    </section>
{%- endmacro -%}

{% set defaultSettings = {
    modifier: '',
    tagSection: 'h2',
    tagTitle: 'h3',
    title: 'Nom Du Bâtiment Consectur Elis passam',
    serviceButtonText: 'Situer le service'
} %}

{#
    ServicesLocation template.
#}

{%- macro ServicesLocation( settings = {} ) -%}
    {% set params = Helpers.merge(defaultSettings, settings) %}
    {% set uid = settings.id or Helpers.unique() %}

    <div class="services-location {{ params.modifier }}">
        {%- if params.modifier == 'is-click-and-roll' -%}
            <{{ params.tagSection }} class="services-location__title-main">
                Chirurgie vitréo rétinienne
            </{{ params.tagSection }}>
        {%- endif -%}
        <div class="services-location__details">
            <{{ params.tagTitle }} class="services-location__title">{{ params.title }}</{{ params.tagTitle }}>
            <p class="services-location__info"><b>3<sub>e</sub> étage</b></p>
            {{ CreateInfoItem(
                href = false,
                icon = 'far fa-map-marker-alt',
                ghost = 'Adresse :',
                text = 'Adresse lorem ipsum mert, 00000  Villedolorsitamet'
            ) }}
        </div>
        <div class="services-location__buttons">
            {{ Link(
                href = '#',
                text = 'Prendre rendez-vous',
                className = 'btn is-ternary',
                icon = 'far fa-user-md'
            ) }}
            {{ Link(
                href = 'javascript:;',
                text = params.serviceButtonText,
                className = 'btn',
                icon = 'far fa-map-marker-alt',
                attrs = {
                    'data-fancybox': 'id-' + uid,
                    'data-type': 'iframe',
                    'data-src': 'localiser-popup.html',
                    'aria-label': params.serviceButtonText + ' sur la carte (fenêtre modale)',
                    'data-dialog-label': params.serviceButtonText + ' ' + params.title + ' sur la carte',
                    'aria-haspopup': 'dialog',
                    'data-role': 'presentation'
                }
            ) }}
        </div>
    </div>
{%- endmacro -%}

{#
    MenuServices template.
#}

{%- macro MenuServices() -%}
    {% set items = [
        'Dolore magna aliqua',
        'Consectetur adipiscing',
        'Sed do eusmod tempor',
        'Incididunt ut labore',
        'Dolore magna aliqua'
    ] %}
    <div class="menu-services">
        <nav class="menu-services__wrapper" role="navigation" aria-label="Menu services">
            <ul class="menu-services__list">
                {% for item in items %}
                    <li class="menu-services__item">
                        <a href="#" class="menu-services__link">
                            <span class="underline">{{ item }}</span>
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </nav>
    </div>
{%- endmacro -%}

{%- macro DoctorItem(imageSizes = ['170x170'], position = true) -%}
    <div class="services-contact__wrapper">
        <div class="services-contact__name-wrapper">
            <h3 class="services-contact__name" id="services-contact-name-{{ titleId }}">
                <span class="services-contact__name-title">Dr</span>
                <span class="services-contact__name-first">Laurent</span>
                <span class="services-contact__name-last">Duisfugiat</span>
                {% if position %}
                    <span class="services-contact__name-position">Chef de service</span>
                {% endif %}
                <span class="services-contact__name-specialite">Spécialité : Biochimie générale</span>
            </h3>
            {%- call Link(
                href = 'mailto:<EMAIL>',
                className = 'btn is-small',
                icon = 'far fa-at',
                attrs = {
                    'aria-describedby': 'services-contact-name-' + titleId
                }
            ) -%}
                <span class="btn__text">ME CONTACTER</span>
                <span class="sr-only">Par courriel</span>
            {%- endcall -%}
        </div>
        {{ Image({
            className: 'services-contact__picture',
            sizes: imageSizes,
            serviceID: range(50) | random
        }) }}
    </div>
{%- endmacro -%}
