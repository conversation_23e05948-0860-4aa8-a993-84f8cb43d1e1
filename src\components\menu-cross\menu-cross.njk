{% from 'views/utils/utils.njk' import setAttr with context %}

{% set defaultLinks = [
    { link: 'PLAN DU SITE', active: false },
    { link: 'MENTIONS LÉGALES', active: false },
    { link: 'PROTECTION DES DONNÉES', active: false },
    { link: 'GESTIONNAIRE DE COOKIE', active: false, tarteaucitron: true }
] %}

{#
    MenuCross template.
    @param {object[]} links - links array
#}
{%- macro MenuCross(links = defaultLinks) -%}
    <div class="menu-cross">
        <div class="menu-cross__wrapper">
            <ul class="menu-cross__list">
                {%- for item in links -%}
                    <li class="menu-cross__item">
                        {% if item.tarteaucitron %}
                            <button type="button"
                                    class="menu-cross__link {{ 'menu-cross__button' if loop.last }}"
                                {{ setAttr('aria-haspopup', 'dialog') }}
                                {{ setAttr('id', 'cookies-popup-btn') }}
                                {{ setAttr('title', 'Gestion des cookies (fenêtre modale)') }}
                                {{ setAttr('onclick', 'tarteaucitron.userInterface.openPanel(); return false;') }}
                            >
                                <span class="menu-cross__text">{{ item.link }}</span>
                            </button>
                        {% else %}
                            <a href="#"
                               class="menu-cross__link {{ 'menu-cross__button' if loop.last }}"
                                {{ setAttr('aria-current', 'page' if item.active else false) }}
                            >
                                <span class="menu-cross__text">{{ item.link }}</span>
                            </a>
                        {% endif %}
                    </li>
                {%- endfor -%}
            </ul>
        </div>
    </div>
{%- endmacro -%}
