{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/utils/utils.njk' import svg -%}
{%- from 'views/core-components/title.njk' import TitleDefault, TitleSecondary -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'views/core-components/icon.njk' import Icon -%}

{% macro SearchServices(consultation = true) %}
    <nav class="search-services js-search-services" role="navigation" aria-label="Rechercher un service, un médecin, une consultation">
        <div class="search-services__container">
            <div class="search-services__wrap">
                <h2 class="search-services__title">
                    <span class="search-services__title-text">Je cherche</span>
                </h2>
                <ul class="search-services__list">
                    <li class="search-services__list-item">
                        <button type="button" class="search-services__list-toggle">
                            <span class="search-services__list-toggle-text">Un service</span>
                            {{ Icon('far fa-chevron-down') }}

                        </button>
                        <div class="search-services__panel">
                            {%- call Button(
                                className = 'btn is-small is-only-icon search-services__close',
                                tooltip = 'Fermer un service',
                                icon = 'far fa-times'
                            ) -%}
                                <span class="ghost">Fermer un service</span>
                            {%- endcall -%}
                            {%- call Form.FormWrapper(legend = 'Rechercher un service') -%}
                                {% call Form.FormGroup() %}
                                    <div class="col-lg-6 col-md-12">
                                         {{ Form.FormField(label = 'Liste des services',
                                            type = 'autocomplete',
                                            wrapperAttrs = {
                                                'data-json-path': 'js/data/test-search-autocomplete.json'
                                            }
                                         ) }}
                                    </div>
                                    <div class="col-lg-6 col-md-12">
                                        {{ Form.FormField(label = 'Médecin',
                                            type = 'autocomplete',
                                            wrapperAttrs = {
                                                'data-json-path': 'js/data/test-search-autocomplete.json'
                                            }
                                        ) }}
                                    </div>
                                    <div class="col-lg-6 col-md-12">
                                        {{ Form.Multiselect(legend = 'Maladies traitées', placeholder = 'Toutes les maladies') }}
                                    </div>
                                {% endcall %}
                                {% call Form.FormActions(className = 'is-center') %}
                                    {{ Button(
                                        className = 'btn is-primary',
                                        type = 'submit',
                                        icon = 'fa fa-search',
                                        tooltip = 'Trouver un service',
                                        text = 'Trouver un service'
                                    ) }}
                                {% endcall %}
                            {% endcall %}
                        </div>
                    </li>
                    <li class="search-services__list-item">
                        <button type="button" class="search-services__list-toggle">
                            <span class="search-services__list-toggle-text">Un médecin</span>
                            {{ Icon('far fa-chevron-down') }}
                        </button>
                        <div class="search-services__panel">
                            {%- call Button(
                                className = 'btn is-small is-only-icon search-services__close',
                                tooltip = 'Fermer un Médecin',
                                icon = 'far fa-times'
                            ) -%}
                                <span class="ghost">Fermer un Médecin</span>
                            {%- endcall -%}
                            {%- call Form.FormWrapper(legend = 'Rechercher un médecin') -%}
                                {% call Form.FormGroup() %}
                                    <div class="col-lg-12 col-md-12">
                                        {{ Form.FormField(
                                            label = 'Son nom',
                                            placeholder = 'Son nom',
                                            type = 'autocomplete',
                                            wrapperAttrs = {
                                                'data-json-path': 'js/data/test-search-autocomplete.json'
                                            }   
                                        ) }}
                                    </div>
                                {% endcall %}
                                {% call Form.FormActions(className = 'is-center') %}
                                    {{ Button(
                                        className = 'btn is-primary',
                                        type = 'submit',
                                        icon = 'fa fa-search',
                                        tooltip = 'Trouver un médecin ',
                                        text = 'Trouver un médecin '
                                    ) }}
                                {% endcall %}
                            {% endcall %}
                        </div>
                    </li>
                    {% if consultation %}
                        <li class="search-services__list-item">
                            <button type="button" class="search-services__list-toggle">
                                <span class="search-services__list-toggle-text">Une consultation</span>
                            </button>
                            <div class="search-services__panel">
                                {%- call Button(
                                    className = 'btn is-small is-only-icon search-services__close',
                                    tooltip = 'Fermer une Consultation',
                                    icon = 'far fa-times'
                                ) -%}
                                    <span class="ghost">Fermer une Consultation</span>
                                {%- endcall -%}
                                {%- call Form.FormWrapper(legend = 'Rechercher une consultation') -%}
                                    {% call Form.FormGroup() %}
                                        <div class="col-lg-6 col-md-12">
                                            {{ Form.Multiselect(legend = 'Son service d’affectation', placeholder = 'Tous les services') }}
                                        </div>
                                        <div class="col-lg-6 col-md-12">
                                            {{ Form.Multiselect(legend = 'Maladies traitées', placeholder = 'Toutes les maladies') }}
                                        </div>
                                        <div class="col-lg-6 col-md-12">
                                            {{ Form.Multiselect(legend = 'Organes traités', placeholder = 'Tous les organes') }}
                                        </div>
                                        <div class="col-lg-6 col-md-12">
                                            {{ Form.Multiselect(legend = 'Thématiques des consultations', placeholder = 'Toutes les thématiques') }}
                                        </div>
                                    {% endcall %}
                                    {% call Form.FormActions(className = 'is-center') %}
                                        {{ Button(
                                            className = 'btn is-primary',
                                            type = 'submit',
                                            icon = 'fa fa-search',
                                            tooltip = 'Trouver une consultation',
                                            text = 'Trouver une consultation'
                                        ) }}
                                    {% endcall %}
                                {% endcall %}
                            </div>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
{% endmacro %}
