{% from 'views/core-components/icon.njk' import Icon with context %}

{% set linksDefault = [
    {
        title: 'Accéder à la page Facebook',
        icon: 'fab fa-facebook-f'
    },
    {
        title: 'Accéder à la page LinkedIn',
        icon: 'fab fa-linkedin-in'
    },
    {
        title: 'Notre Twitter',
        icon: 'fa-brands fa-x-twitter'
    },
    {
        title: 'Notre Youtube',
        icon: 'fab fa-youtube'
    },
    {
        title: 'Notre Instagram',
        icon: 'fab fa-instagram'
    }
] %}

{#
    Insert socialLinks block.
    @param {string} className - custom class name modifier.
    @param {object[]} links - links array.
#}
{% macro SocialLinks(className = '', links = linksDefault, classNameLinks) %}
    {% if links %}
        <ul class="social-links {{ className }}">
            {% for item in links %}
                <li class="social-links__item">
                    <a href="#" class="social-links__link js-tooltip {{ classNameLinks if classNameLinks }}"
                       target="_blank"
                       data-content="{{ item.title }}"
                    >
                        {{ Icon(item.icon) }}
                        <span class="social-links__link-text ghost">{{ item.title }}</span>
                    </a>
                </li>
            {% endfor %}
            {{ caller() if caller }}
        </ul>
    {% endif %}
{% endmacro %}

{#
    SocialMenu template.
#}
{%- macro SocialMenu() -%}
    <div class="social-menu">
        {{ SocialLinks(
            className = 'is-floating',
            classNameLinks = 'is-tooltip-left',
            links = [
                {
                    title: 'Facebook',
                    icon: 'fab fa-facebook-f'
                },
                {
                    title: 'Twitter',
                    icon: 'fa-brands fa-x-twitter'
                },
                {
                    title: 'YouTube',
                    icon: 'fab fa-youtube'
                },
                {
                    title: 'Instagram',
                    icon: 'fab fa-instagram'
                }
            ]
        ) }}
    </div>
{%- endmacro -%}
