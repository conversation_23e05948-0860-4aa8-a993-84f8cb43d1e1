.hero {
    &.is-type-3 {
        margin-bottom: 0;
        margin-top: 192px;

        @include breakpoint(medium down) {
            margin-top: 84px;
        }

        .hero-item {
            &__content {
                @extend %link-block-context;
                @extend %underline-context;
                @include trs;
                @include absolute(null, null, -8px, 0);
                box-sizing: border-box;
                color: $color-white;
                display: block;
                max-width: 555px;
                overflow: hidden;
                padding-top: 10px;
                text-align: left;
                text-decoration: none;
                text-shadow: 0 0 6px rgba($color-black, 0.16);
                z-index: 1;

                @include breakpoint(medium down) {
                    bottom: -6px;
                    max-width: 355px;
                }

                @include breakpoint(small down) {
                    bottom: -46px;
                    left: 0;
                    padding: 0;
                    transform: none;
                    width: 100%;
                }

                &::before {
                    @include absolute(15px, 15px, 15px, 15px);
                    @include trs;
                    border: 2px solid rgba($color-white, 0.5);
                    content: "";
                    opacity: 0;
                    visibility: hidden;
                }

                @include on-event {
                    &[href] {
                        &::before {
                            @include absolute(0, 0, 0, 0);
                            opacity: 1;
                            visibility: visible;
                        }
                    }
                }
            }

            &__category {
                color: $color-white;
                margin-bottom: 20px;
            }

            &__title {
                @include font(null, 3.5rem, var(--fw-bold));
                color: $color-white;
                line-height: 1.4;
                margin-top: 6px;
                
                @include breakpoint(medium down) {
                    font-size: 2.3rem;
                    line-height: 1.4;
                }

                @include breakpoint(small down) {
                    font-size: 2.3rem;
                    line-height: 1.8;
                }

                .js-inline-bg span {
                    background-color: var(--color-1--2);
                    bottom: 14px;
                    padding-bottom: 6px;
                    padding-left: 30px;
                    padding-right: 20px;
                    position: relative;

                    @include breakpoint(medium down) {
                        bottom: 12px;
                        padding-left: 18px;
                    }

                    @include breakpoint(small down) {
                        bottom: 19px;
                    }

                    &:first-of-type {
                        bottom: 9px;
                        padding-top: 10px;
                        
                        @include breakpoint(medium down) {
                            bottom: 5px;
                            padding-bottom: 6px;
                            padding-top: 3px;
                        }
                    }
                }
            }

            &__picture {
                &::after {
                    height: 705px;

                    @include breakpoint(medium down) {
                        height: 488px;
                    }

                    @include breakpoint(small down) {
                        height: 368px;
                    }
                }
            }
        }
    }
}
