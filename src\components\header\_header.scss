.header-wrapper {
    $this: &;

    background-color: var(--color-1--1);
    display: block;
    width: 100%;
    z-index: auto !important;

    &__container {
        @extend %container-fluid;
        margin: 0 auto;
        padding: 0;
        position: relative;

        @include breakpoint(medium down) {
            max-width: 100%;
        }
    }

    .has-page-image &,
    body.home-page &,
    body.home-hospital-page & {
        @include absolute(0, null, null, 0);
        z-index: 100;
    }
}

.header {
    $this: &;

    @include trs(background);
    background-color: var(--color-1--1);
    height: 79px;
    opacity: 1;
    padding: 9px 0;
    z-index: 20;

    .home-page & {
        background-color: transparent;
        height: 100px;
        padding: 25px 0;
        position: relative;

        @include breakpoint(medium down) {
            @include fixed();
            background-color: var(--color-1--1);
            height: 84px;
            padding: 12px 6px;
            width: 100%;
        }

        @include breakpoint(small down) {
            padding-inline: 0;
        }

        #{$this} {
            @include breakpoint(large only) {
                &__inner {
                    margin: 0 auto;
                    max-width: 1480px;
                    padding-inline: 20px;
                }

                &__logo {
                    @include size(306px, 76px);
                    margin-left: 81px;
                    margin-top: 20px;
                }
            }

            &__logo {
                @include media-max(1400) {
                    margin-left: 0;
                }
            }
        }
    }

    @include breakpoint(medium down) {
        height: 84px;
        padding: 12px 0;

        &.js-fixed-el-abs {
            left: 0;
            transform: none;
        }
    }

    &__nav-toggle {
        height: fit-content;
        margin-right: 2px;
        margin-top: 21px;

        @include breakpoint(medium down) {
            margin-right: 0;
            margin-top: 17px;
        }
    }

    &__search {
        height: fit-content;
        margin-right: 38px;
        margin-top: 17px;
        position: relative;

        @include breakpoint(medium down) {
            margin-top: 14px;
        }

        &::after {
            @include size(1px, 22px);
            @include absolute(4px, null, null, 41px);
            border: 1px solid $color-white;
            content: "";
        }
    }

    &__inner {
        align-items: flex-start;
        display: flex;
        justify-content: space-between;
        margin: 0 auto 0 17px;
        max-width: 1528px;
        padding: 0 14px 0 1px;

        @include breakpoint(medium down) {
            margin-left: 5px;
            min-height: 65px;
            padding-left: 14px;
        }
        
        @include breakpoint(small down) {
            margin-left: 0;
            padding-left: 10px;
        }
    }

    &__logo {
        @include size(231px, 57px);
        @include trs;
        align-content: center;
        align-items: center;
        display: flex;
        padding: 0;

        @include breakpoint(medium down) {
            @include size(220px, 55px);
        }

        #{$this}:not(.js-fixed-el) & {
            body:not(.is-mnv-opened) .has-page-image &,
            body:not(.is-mnv-opened).home-page &,
            body:not(.is-mnv-opened).home-hospital-page & {
                a.logo {
                    color: $color-white;
                }
            }
        }
    }

    &__components {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
    }

    &__actions {
        align-items: center;
        display: flex;
        margin-right: 34px;

        @include breakpoint(medium down) {
            display: none;
        }

        .btn {
            font-size: 1.6rem;
            font-weight: var(--fw-bold);
            margin-top: 8px;
            padding: 8px 16px;

            span:not([class*="fa-"]) {
                letter-spacing: 0.48px;
            }

            &.emergency {
                background-color: $color-3--8 !important;
                border-color: $color-3--8 !important;
                color: $color-black !important;
                margin-right: 5px;
                padding: 8px 15px;

                @include on-event() {
                    color: $color-black !important;
                }
            }

            &.medicine-consultation {
                background-color: transparent !important;
                border-color: transparent !important;
                color: $color-white !important;
                margin-left: 9px;
                margin-right: 89px;

                @include media-max(1400) {
                    margin-right: 30px;
                }

                @include on-event() {
                    background-color: transparent !important;
                    border-color: transparent !important;
                    color: $color-white !important;
                    text-decoration: underline;
                }
            }

            &.endowment-fund {
                background-color: transparent !important;
                border-color: $color-white !important;
                color: $color-white !important;
                padding: 13px 25px;

                .btn__text {
                    letter-spacing: 0;
                }

                @include on-event() {
                    color: $color-black !important;
                }
            }

            @include on-event() {
                background-color: var(--color-2--1) !important;
                border-color: var(--color-2--1) !important;
                color: $color-white !important;
            }
        }
    }

    &__lang {
        @include size(54px, 80px);
        align-items: center;
        display: flex;
        margin-right: 35px;

        @include breakpoint(medium down) {
            display: none;
        }
    }

    &__nav {
        align-items: center;
        display: flex;
        flex-grow: 1;
        justify-content: center;
        width: 1%;
    }

    &.has-nav-bottom {
        @include breakpoint(large) {
            padding-bottom: 0;
        }

        #{$this}__inner {
            @include breakpoint(large) {
                flex-wrap: wrap;
            }
        }

        #{$this}__components {
            @include breakpoint(large) {
                flex-basis: calc(100% - 404px);
            }
        }

        #{$this}__nav {
            @include breakpoint(large) {
                background-color: var(--color-1--1);
                margin: 25px 0 0;
                min-height: 80px;
                position: relative;
                width: 100%;

                &::before,
                &::after {
                    @include absolute(0, null, 0, null);
                    background-color: var(--color-1--1);
                    content: "";
                    width: 1000vh;
                    z-index: -1;
                }

                &::before {
                    left: 0;
                }

                &::after {
                    right: 0;
                }
            }

            @include breakpoint(medium down) {
                display: none;
            }
        }
    }

    &.js-fixed-el {
        @include fixed(0, null, null, null);
        background-color: var(--color-1--1);
        z-index: 100;

        @include breakpoint(large only) {
            #{$this} {
                &__inner {
                    margin-left: 0;
                    max-width: 1540px;
                    padding-left: 0;
                }

                &__logo {
                    @include size(231px, 57px);
                    margin-left: 10px;
                    margin-top: 0;
                }
            }
        }

        @include breakpoint(medium down) {
            z-index: 99999;
        }
    }

    &:not(.js-fixed-el) {
        body:not(.is-mnv-opened) .has-page-image:not(.has-secondary-heading) &,
        body:not(.is-mnv-opened).home-page &,
        body:not(.is-mnv-opened).home-hospital-page & {
            .header__actions {
                .btn {
                    background-color: transparent;
                    border-color: $color-white;
                    color: $color-white;

                    @include on-event {
                        background-color: var(--color-1--1);
                        border-color: var(--color-1--1);
                        color: $color-white;
                    }
                }
            }
        }
    }

    &:not(.js-fixed-el).has-logo-center {
        #{$this}__logo {
            @include absolute(20px, 50%);
            transform: translate(50%, 100%);

            @include breakpoint(medium down) {
                top: 125px;
            }

            @include breakpoint(small down) {
                top: 75px;
            }
        }

        #{$this}__actions {
            display: flex;
            margin-right: auto;

            @include breakpoint(small down) {
                height: 45px;
            }
        }
    }
}
