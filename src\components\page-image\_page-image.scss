.page-image {
    @include size(100%, 620px);
    display: block;
    position: relative;

    @include breakpoint(medium down) {
        height: 298px;
        margin-top: 84px;
    }

    @include breakpoint(small down) {
        height: 140px;
    }

    &::before {
        @include absolute(0, 0, null, 0);
        background: linear-gradient(0deg, rgba($color-black, 0) 0%, rgba($color-black, 0.52) 64%, rgba($color-black, 0.6) 100%);
        content: '';
        height: 310px;
        z-index: 1;

        @include breakpoint(medium down) {
            height: 226px;
        }

        @include breakpoint(small down) {
            height: 120px;
        }
    }

    &__container {
        @extend %container;
        @extend %container-fluid;
        height: 100%;
    }

    &__picture {
        @include size(100%);
        display: block;

        img {
            @include object-fit;
            @include size(100%, 100%);
            display: block;
        }
    }
}
