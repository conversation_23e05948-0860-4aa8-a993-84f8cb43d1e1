.reviews-item {
    $this: &;

    @extend %link-block-context;
    align-items: flex-start;
    display: flex;
    flex-grow: 1;
    max-width: 100%;
    width: 100%;

    @include breakpoint(medium down) {
        flex-direction: column;
    }

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
    }

    &__picture-wrap {
        @include size(250px, auto);
        flex-shrink: 0;
        position: relative;

        @include breakpoint(medium down) {
            width: 200px;
        }
    }

    &__picture {
        @include size(100%, auto);

        img {
            @include object-fit();
            display: block;
        }
    }

    &__content {
        flex-grow: 1;
        padding-left: 80px;
        width: 1%;

        @include breakpoint(medium down) {
            padding-left: 34px;
            width: 100%;
        }

        @include breakpoint(small down) {
            padding: 38px 0 0 0;
            text-align: center;
            width: 100%;
        }
    }

    &__category {
        @include font(null, 1.4rem, var(--fw-medium));
        color: var(--color-1--1);
        letter-spacing: 2.52px;
        margin-bottom: 13px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            font-size: 1.2rem;
        }
    }

    &__quote {
        @include font(var(--typo-1), 2rem, var(--fw-normal), italic);
        color: $color-3--4;
        line-height: 1.5;
        margin: 0 0 20px;
        padding-bottom: 25px;
        position: relative;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
        }
    }

    &__name {
        @include font(var(--typo-1), 2.2rem, var(--fw-bold));
        line-height: 1.25;
        margin: 0 0 5px;

        @include breakpoint(medium down) {
            font-size: 2rem;
        }
    }

    &__info {
        margin-left: 29px;

        @include breakpoint(medium down) {
            margin-left: 0;
        }

        @include breakpoint(small down) {
            margin-left: 19px;
        }
    }

    &__name-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__function {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $color-3--4;
        line-height: 1.25;
    }

    &__teaser {
        margin-top: 10px;
    }

    .reviews-home & {
        &__picture-wrap {
            @include size(189px);

            @include breakpoint(medium down) {
                @include size(136px);
                margin-top: 12px;
            }

            @include breakpoint(small down) {
                @include size(213px);
                margin-left: 20px;
                margin-top: 0;
            }
        }

        &__content {
            padding: 0;
        }

        &__quote {
            color: $color-white;
            display: flex;
            flex-direction: column;
            font-size: 2rem;
            margin-bottom: 18px;
            margin-left: -32px;
            margin-top: 56px;
            padding: 0;

            @include breakpoint(medium down) {
                font-size: 1.7rem;
                margin: -10px 0 0;
            }

            @include breakpoint(small down) {
                align-items: center;
                font-size: 2rem;
                // margin: -28px auto 0;
                margin: -13px auto 0 20px;
            }

            span {
                background-color: var(--color-1--5);
                padding-bottom: 3px;
                padding-left: 17px;
                padding-right: 15px;
                width: fit-content;
            }
        }

        &__name {
            font-size: 2.4rem;

            @include breakpoint(medium down) {
                margin-bottom: 0;
                margin-top: 20px;
            }

            @include breakpoint(small down) {
                margin: 31px auto 0;
            }
        }
    }
}
