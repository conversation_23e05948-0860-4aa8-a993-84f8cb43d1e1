.album-item {
    $this: &;

    @extend %link-block-context;
    display: flex;
    flex-direction: column-reverse;
    justify-content: flex-end;

    @include breakpoint(medium down) {
        align-items: flex-start;
        flex-direction: row-reverse;
    }

    &__picture {
        flex-shrink: 0;
        width: 100%;

        @include breakpoint(medium down) {
            max-width: 266px;
        }

        @include breakpoint(small down) {
            max-width: 100px;
        }

        img {
            @include size(100%, auto);
            display: block;
            max-width: 100%;
        }
    }

    &__content {
        padding: 20px 0 0;

        @include breakpoint(medium down) {
            padding: 30px 40px 0;
        }

        @include breakpoint(small down) {
            padding: 0 0 0 10px;
        }
    }

    &__category {
        margin-bottom: 15px;

        @include breakpoint(small down) {
            margin-bottom: 10px;
        }
    }

    &__title {
        margin-bottom: 20px;

        @include breakpoint(small down) {
            margin-bottom: 10px;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__media {
        @include font(var(--typo-1), 1.2rem, var(--fw-normal));
        color: $color-3--5;
        padding-left: 45px;
        position: relative;
        text-transform: uppercase;

        @include breakpoint(small down) {
            padding-left: 0;
        }

        &::before {
            @include absolute(50%, null, null, 0);
            @include size(35px, 4px);
            background-color: var(--color-2--1);
            content: '';
            transform: translateY(-50%);

            @include breakpoint(small down) {
                content: none;
            }
        }
    }

    &__type {
        @include absolute(null, 0, 0);
        @include font(null, 1.2rem, var(--fw-normal));
        @include size(45px);
        align-items: center;
        background-color: var(--color-1--1);
        color: $color-white;
        display: flex;
        justify-content: center;

        &.is-video {
            background-color: var(--color-2--1);
            color: var(--color-1--2);
        }
    }

    &.is-overlapped {
        background-color: var(--color-1--1);
        position: relative;

        @include on-event {
            #{$this}__content {
                height: 100%;
            }
        }

        #{$this}__picture {
            max-width: initial;
        }

        #{$this}__content {
            @include absolute(null, null, 0, 0);
            @include trs;
            background-image: linear-gradient(180deg, transparent 0%, rgba($color-black, 0.6) 52%, rgba($color-black, 0.71) 100%);
            display: flex;
            flex-direction: column;
            height: 300px;
            justify-content: flex-end;
            padding: 40px 70px;
            width: 100%;
            z-index: 2;

            @include breakpoint(medium down) {
                height: 100%;
                padding: 15px 50px 15px 35px;
            }

            @include breakpoint(small down) {
                padding: 18px 50px 18px 30px;
            }
        }

        #{$this}__title-link {
            &:focus-visible {
                &::after {
                    outline-offset: -3px;
                }
            }
        }
    }
}
