{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- from 'views/core-components/carousel.njk' import CarouselWrapper -%}
{%- from 'views/utils/utils.njk' import svg -%}

{#
    QuickLinksInfo template.
#}
{% macro QuicklinksInfo(
    nameIcon = 'avatar',
    title = 'Mes <br>démarches',
    teaser = 'J’accède à mon compte pour mes démarches, mes abonnements, ...'
) %}
    <div class="quicklinks-info">
        <div class="quicklinks-info__text">
            <div class="quicklinks-info__svg-wrapper" aria-hidden="true">
                {{ svg('icons/' + nameIcon, 60, 60) }}
            </div>
            <h3 class="quicklinks-info__title">
                <a href="#" class="quicklinks-info__link">
                    <span class="underline">{{ title | safe }}</span>
                </a>
            </h3>
        </div>
        <p class="quicklinks-info__teaser">{{ teaser }}</p>
        <span class="quicklinks-info__icon far fa-long-arrow-right" aria-hidden="true"></span>
    </div>
{% endmacro %}

{#
    QuickLinksItem template.
#}
{%- macro QuickLinksItem(
    nameIcon = 'archery',
    title = lorem(1, 'word'),
    modifier = ''
) -%}
    <div class="quicklink-item {{ modifier }}">
        <div class="quicklink-item__svg-wrapper" aria-hidden="true">
            {{ svg('icons/' + nameIcon, 60, 60) }}
        </div>
        <a href="#" class="quicklink-item__text">
            <span class="underline">{{ title }}</span>
        </a>
    </div>
{%- endmacro -%}

{#
    QuickLinksBlock template.
#}
{%- macro QuickLinksBlock(settings = {}) -%}
    {% set params = Helpers.merge({
        listLinks: [
            ['man-and-trash', 'Propreté'],
            ['payment', 'Paiement en ligne'],
            ['cone', 'Infos travaux'],
            ['man-and-message', 'Je signale...'],
            ['route', 'Annuaires'],
            ['family', 'Portail famille'],
            ['phonendoscope', 'Santé'],
            ['communication', 'Contact']
        ],
        modifier: '',
        type: 'carousel',
        carouselItemsToShow: [4, 3, 1],
        itemsCount: 3,
        carouselAttrs: {
            'aria-label' : 'Liens'
        }
    }, settings) %}
    <div class="quicklinks {{ params.modifier }}">
        {% if params.type === 'list' %}
            <div class="quicklinks__wrapper">
                <ul class="quicklinks__list">
                    {% for link in params.listLinks %}
                        {% if loop.index <= params.itemsCount %}
                            <li class="quicklinks__list-item">
                                {{ QuickLinksItem(
                                    nameIcon = link[0],
                                    title = link[1]
                                ) }}
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
        {% if params.type === 'carousel' %}
            {% call CarouselWrapper(settings = {
                wrapperClassName: 'quicklinks-block',
                wrapperTag: 'ul',
                itemsToShow: params.carouselItemsToShow,
                enableNavigationAlign: false,
                arrows: {
                    outside: true,
                    next: {
                        text: 'Accès rapide suivant',
                        icon: 'far fa-long-arrow-right'
                    },
                    prev: {
                        text: 'Accès rapide précédent',
                        icon: 'far fa-long-arrow-left'
                    }
                },
                actions: false,
                autoplay: false,
                wrapperAttrs: params.carouselAttrs
            }) %}
                {% for link in params.listLinks %}
                    <li class="quicklinks-block__item swiper-item">
                        {{ QuickLinksItem(
                            nameIcon = link[0],
                            title = link[1]
                        ) }}
                    </li>
                {% endfor %}
            {% endcall %}
        {% endif %}
    </div>
{%- endmacro -%}

{#
    QuickLinksContent template.
#}
{%- macro QuickLinksContent(
    className = 'quicklinks-content',
    titleText = 'Accès rapides',
    blockSettings = {}
) -%}
    {% if titleText %}
        {% set ariaLabel = titleText %}
    {% else %}
        {% set ariaLabel = 'Accès rapides' %}
    {% endif %}
    {% call Section(
        className = className,
        container = false,
        tag = 'nav',
        attrs = {
            'role': 'navigation',
            'aria-label': ariaLabel
        }) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ QuickLinksBlock(blockSettings) }}
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    QuickLinksHome template.
#}
{%- macro QuickLinksHome(
    className = 'quicklinks-home has-info-block',
    titleText = '',
    shadowBlock = true,
    blockSettings = {
        modifier: 'is-inverted'
    }
) -%}
    {% if titleText %}
        {% set ariaLabel = titleText %}
    {% else %}
        {% set ariaLabel = 'Accès rapides' %}
    {% endif %}
    {% call Section(
        className = className,
        container = 'quicklinks-home__container',
        tag = 'nav',
        attrs = {
            'role': 'navigation',
            'aria-label': ariaLabel
        }) %}
        <h2 class="ghost">{{ titleText }}</h2>
        <div class="section__content {{ 'has-box-shadow' if shadowBlock }}">
            {{ QuickLinksBlock(blockSettings) }}
            {{ QuicklinksInfo(
                nameIcon = 'family',
                title = 'Portail <br>famille',
                teaser = 'Texte 85 Caractères ipsum dolor amet consetetur sadipscing prelitr diam nonumy eirmod'
            ) }}
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    QuickLinksHomeFluid template.
#}
{%- macro QuickLinksHomeFluid(
    className = 'quicklinks-home is-fluid has-info-block has-box-shadow',
    titleText = 'Accès rapides',
    blockSettings = {
        modifier: 'is-inverted'
    }
) -%}
    {% if titleText %}
        {% set ariaLabel = titleText %}
    {% else %}
        {% set ariaLabel = 'Accès rapides' %}
    {% endif %}
    {% call Section(
        className = className,
        container = 'quicklinks-home__container',
        tag = 'nav',
        attrs = {
            'role': 'navigation',
            'aria-label': ariaLabel
        }) %}
        <h2 class="ghost">{{ titleText }}</h2>
        <div class="section__content">
            {{ QuickLinksBlock(blockSettings) }}
            {{ QuicklinksInfo(
                nameIcon = 'family',
                title = 'Portail <br>famille',
                teaser = 'Texte 85 Caractères ipsum dolor amet consetetur sadipscing prelitr diam nonumy eirmod'
            ) }}
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    QuickLinksHomeLight template.
#}
{%- macro QuickLinksHomeLight(
    className = 'quicklinks-home is-light',
    titleText = 'Accès rapides',
    shadowBlock = true,
    blockSettings = {
        modifier: 'is-inverted',
        carouselItemsToShow: [7, 3, 1]
    }
) -%}
    {% if titleText %}
        {% set ariaLabel = titleText %}
    {% else %}
        {% set ariaLabel = 'Accès rapides' %}
    {% endif %}
    {% call Section(
        className = className,
        container = 'quicklinks-home__container',
        tag = 'nav',
        attrs = {
            'role': 'navigation',
            'aria-label': ariaLabel
        }) %}
        <h2 class="ghost">{{ titleText }}</h2>
        <div class="section__content {{ 'has-box-shadow' if shadowBlock }}">
            {{ QuickLinksBlock(blockSettings) }}
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    QuickAccessHome template.
#}
{%- macro QuickAccessHome(
        className = 'quicklinks-home',
        sections = [
            {
                title:"JE SOUHAITE...",
                icon: "ico-1",
                quicklinks: [
                    "Prendre un RDV",
                    "Accéder à mes résultats",
                    "Postuler / Candidater",
                    "Donner mon avis",
                    "Payer en ligne",
                    "Une demande de rétrocession",
                    "Faire un don",
                    "M'informer sur les urgences"
                ]
            },
            {
                title:"JE PRÉPARE...",
                icon: "ico-2",
                quicklinks:[
                    "Une consultation",
                    "Une hospitalisation",
                    "Un accueil Handicap",
                    "Une naissance",
                    "Un accueil Gériatrie",
                    "Ma venue à l'hôpital",
                    "Ma sortie de l’hôpital",
                    "Ma visite à un proche"
                ]
            }
        ]
    ) -%}
    {% call Section(className = className, container = 'quicklinks-home__container', tag = 'nav') %}
        <h2 class="ghost">{{ titleText }}</h2>
        <div class="section__content">
            {% for item in sections %}
                <div class="quicklinks-home__side">
                    <div class="quicklinks-home-side__title">
                        {{ svg('icons/' + item.icon, 53, 65) }}
                        <span>{{ item.title | safe }}</span>
                    </div>
                    <ul class="quicklinks-home-side__quicklinks">
                        {% for quicklink in item.quicklinks %}
                            <li class="quicklinks-home-side__quicklinks-item">
                                <a href="#" class="quicklinks-home-side__quicklinks-link">{{ quicklink | safe }}</a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            {% endfor %}
        </div>
    {% endcall %}
{%- endmacro -%}
