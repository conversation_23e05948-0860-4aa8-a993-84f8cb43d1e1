{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'components/share/share.njk' import Share -%}

{#
    Tools template.
#}
{%- macro Tools(
    rssBtnText = false,
    favoriteBtn = false,
    pdfBtn = false
) -%}
    <div class="tools">
        <ul class="tools__list" aria-roledescription="liste d'outils">
            {% if favoriteBtn %}
                <li class="tools__item">
                    <button
                        type="button"
                        class="tools-btn js-tooltip js-favorites"
                        data-endpoint="/js/data/favorites-success.json"
                        data-page-data='{
                            "id": 123,
                            "content": "Test",
                            "title": "Test title"
                        }'
                        data-content="Ajouter cette page aux favoris"
                        data-add-favorite="Ajouter cette page aux favoris"
                        data-remove-favorite="Supprimer cette page des favoris"
                    >
                        {{ Icon('far fa-star') }}
                        <span class="ghost">Ajouter cette page aux favoris</span>
                    </button>
                </li>
            {% endif %}
            {% if rssBtnText %}
                <li class="tools__item">
                    <a
                        href="#"
                        class="tools-btn js-tooltip"
                        data-content="{{ rssBtnText }}"
                    >
                        {{ Icon('far fa-rss') }}
                        <span class="ghost">{{ rssBtnText }}</span>
                    </a>
                </li>
            {% endif %}
            {% if pdfBtn %}
                <li class="tools__item">
                    <a
                        href="#"
                        rel="nofollow"
                        class="tools-btn js-tooltip js-generate-pdf"
                        data-content="Générer la page en PDF"
                    >
                        {{ Icon('far fa-file-pdf') }}
                        <span class="ghost">Exporter la page au format PDF (1.5Mo environ)</span>
                    </a>
                </li>
            {% endif %}
            <li class="tools__item">
                {{ Share() }}
            </li>
        </ul>
    </div>
{%- endmacro -%}
