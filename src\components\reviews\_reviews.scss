.reviews-home {
    width: 50%;

    @include breakpoint(small down) {
        width: 100%;
    }

    &__container {
        @extend %container;
        padding-left: 60px;
        padding-top: 66px;

        @include breakpoint(medium down) {
            padding-left: 46px;
            padding-right: 30px;
            padding-top: 28px;
        }

        @include breakpoint(small down) {
            max-width: 768px;
            padding-inline: 0;
            padding-top: 43px;
        }
    }

    &.section {
        margin-bottom: 64px;
        margin-top: 0;

        @include breakpoint(small down) {
            margin-bottom: 0;
            margin-left: -10px;
            padding-bottom: 61px;
            position: relative;

            &::before {
                @include size(1000VW, 100%);
                @include absolute(0, 0, null, 50%);
                background-color: $color-3--7;
                content: "";
                transform: translate(-50%);
                z-index: -1;
            }
        }
    }

    .section {
        &__more-links {
            justify-content: flex-start;
            padding-left: 510px;

            @include breakpoint(medium down) {
                padding-left: 300px;
            }

            @include breakpoint(small down) {
                justify-content: center;
                padding-left: 0;
            }
        }
    }

    .reviews-item {
        margin-bottom: 50px;

        @include breakpoint(medium down) {
            margin-bottom: 30px;
        }

        @include breakpoint(small down) {
            margin-bottom: 25px;
        }
    }
}
