{%- from 'components/breadcrumbs/breadcrumbs.njk' import Breadcrumbs -%}
{%- from 'components/tools/tools.njk' import Tools -%}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{#
    Navbar tempalte.
#}
{%- macro Navbar(
    className = 'has-negative-margin',
    pageTitle = 'Dans cette rubrique',
    dropdown = true,
    links = [
        'Lorem ipsum',
        'Consectur',
        pageTitle or 'Dans cette rubrique'
    ],
    useTools = true,
    rssBtnText,
    pdfBtn = true
) -%}
    <div class="navbar {{ className }}">
        <div class="navbar__wrapper">
            {% if className == 'is-reversed' %}
                {% if useTools %}
                    <div class="navbar__tools">
                        {{ Tools(rssBtnText, pdfBtn = pdfBtn) }}
                    </div>
                {% endif %}
                <div class="navbar__breadcrumbs">
                    {{ Breadcrumbs(pageTitle, dropdown, links) }}
                </div>
            {% else %}
                <div class="navbar__breadcrumbs">
                    {{ Breadcrumbs(pageTitle, dropdown, links) }}
                </div>
                {% if useTools %}
                    <div class="navbar__tools">
                        {{ Tools(rssBtnText, pdfBtn = pdfBtn) }}
                    </div>
                {% endif %}
            {% endif %}

        </div>
    </div>
{%- endmacro -%}

{#
    NavbarSG template.
    Styleguide template.
#}
{%- macro NavbarSG() -%}
    {% call SG.Section('navbar') %}
    <h2 class="styleguide-section__title">Navbar</h2>
    {%- call SG.Preview() -%}
    <div class="flex-row">
        <div class="col-sm-12">
            <h3 class="styleguide-section__subtitle">Default navbar</h3>
            <div class="has-mb-3">
                {{ Navbar() }}
            </div>
            <div class="has-mb-3">
                {{ Navbar(dropdown = false, links = [pageTitle or 'Dans cette rubrique']) }}
            </div>
        </div>
    </div>
    {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}