{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/video.njk' import VideoWrapper -%}
{%- from 'views/core-components/carousel.njk' import CarouselWrapper -%}
{% from 'views/utils/utils.njk' import setAttr, wrapper with context %}
{%- from 'components/search/search.njk' import SearchForm, SearchTags -%}
{%- from 'components/search-services/search-services.njk' import SearchServices -%}

{#
    HeroItem template.
#}
{%- macro HeroItem(
    category = 'Lorem ipsum',
    title = 'Maecenas mollis, augue vitae  sagittis',
    imageSizes = ['480x475?479', '768x475?767', '1280x620?1279', '1920x1080'],
    tag = 'span',
    label = '',
    hasVideo = false,
    hasSearch = false,
    hasContent = false,
    isCarousel = false,
    hideImageLink = true,
    hasInlineBg = true,
    hasCaption = false
) -%}
    <div class="hero-item {{ 'js-content-root' if hasVideo }}" {{ setAttr('aria-label', label) }}>
        {% if hasCaption %}
            <figure class="hero-item__figure" role="group" aria-label="{{ title }}">
        {% endif %}
        {% if hasContent and not hasCaption %}
            <a href="#" class="hero-item__content {{ 'js-content' if hasVideo }}">
                <{{ tag }} class="hero-item__title {{ 'js-slide-title' if isCarousel }}">
                {% if category %}
                    <span class="theme is-large hero-item__category">{{ category }}</span>
                    <span class="sr-only">:</span>
                {% endif %}
                {% if hasInlineBg %}
                    <span class="js-inline-bg" data-max-length="23">{{ title }}</span>
                {% else %}
                    {{ title }}
                {% endif %}
                </{{ tag }}>
            </a>
        {% endif %}
        <div class="hero-item__media">
            {% if not hasVideo %}
                <a href="#" class="hero-item__link {{ 'js-unchanged-tabindex' if isCarousel and hasContent }}" {% if hideImageLink and hasContent %} role="presentation" tabindex="-1"{% endif %}>
                    {{ Image({
                        className: 'hero-item__picture',
                        sizes: imageSizes,
                        alt: lorem(1),
                        hasLazyLoad: false
                    }) }}
                </a>
            {% endif %}
            {% if hasVideo %}
                <div class="hero-item__video">
                    {{ VideoWrapper({
                        actions: true,
                        autoplay: false,
                        ariaLabel: 'vidéo décorative'
                    }) }}
                </div>
            {% endif %}
        </div>
        {% if hasCaption %}
            <figcaption class="hero-item__caption">
                <a href="#" class="hero-item__content  {{ 'js-content' if hasVideo }}">
                    <{{ tag }} class="hero-item__title {{ 'js-slide-title' if isCarousel }}">
                        {% if category %}
                            <span class="theme is-large hero-item__category">{{ category }}</span>
                            <span class="sr-only">:</span>
                        {% endif %}
                        {% if hasInlineBg %}
                            <span class="js-inline-bg" data-max-length="23">Interdépartemental {{ title }}</span>
                        {% else %}
                            {{ title }}
                        {% endif %}
                    </{{ tag }}>
                </a>
            </figcaption>
        {% endif %}
        {% if hasCaption %}
            </figure>
        {% endif %}
        {% if hasSearch %}
            <div class="hero-item__search {{ 'js-content' if hasVideo }}">
                <div class="hero-search" role="search">
                    {{ SearchForm(
                        buttonClassName = 'hero-search__btn',
                        label = 'Que recherchez-vous ?',
                        labelModifier = 'ghost'
                    ) }}
                </div>
                <div class="hero-item__keywords">
                    {{ SearchTags() }}
                </div>
            </div>
        {% endif %}
    </div>
{%- endmacro -%}

{%- set arrowsDefaults = {
    next: {
        text: 'Mise en avant suivante'
    },
    prev: {
        text: 'Mise en avant précédente'
    }
} -%}

{#
    HeroCarousel template.
#}
{%- macro HeroCarousel(
    count = 4,
    hasInlineBg = false,
    hideArrows = false,
    category = true,
    hasCaption = false,
    hasContent = true
) -%}
    {% call CarouselWrapper(settings = {
        wrapperClassName: 'hero-carousel',
        wrapperAttrs: {
            'aria-label': 'Mise en avant'
        },
        wrapperAriaAttrs: {
            'aria-label': 'contrôle du diaporama'
        },
        itemsToShow: 1,
        enableNavigationAlign: true,
        enableInteractiveAlign: true,
        loop: false,
        arrows: arrowsDefaults if not hideArrows else false,
        effect: 'fade',
        pagination: 'outside',
        actionsWrapper: true,
        actionsWrapperModifier: 'has-shadow' if count > 1
    }) %}
        {% for item in range(count) %}
            {{ HeroItem(
                title = 'Bienvenue dans un monde plus hospitalier. ',
                label = loop.index + ' sur ' + count,
                tag = 'h3',
                hasContent = hasContent,
                isCarousel = true,
                hasInlineBg = hasInlineBg,
                category = category,
                hasCaption = hasCaption
            ) }}
        {% endfor %}
    {% endcall %}
{%- endmacro -%}

{#
    SliderItem template.
#}

{% macro SliderItem(
    category = 'Lorem ipsum',
    title = 'Maecenass mollis, augue vita  sagittis',
    hasContent = true,
    linkTag = '',
    linkHref = '#'
) %}
    {% set id = Helpers.unique() %}
    <article class="slider-item">
        <h3 class="slider-item__button-wrap">
            <button class="slider-item__toggle js-tooltip" type="button" aria-controls="{{ id }}" data-content="{{ title }}">
                {{ Image({
                    className: 'slider-item__image',
                    sizes: ['499x375?767', '952x714'],
                    type: false,
                    service: 'picsum',
                    serviceID: range(1000) | random,
                    hasLazyLoad: false
                }) }}
                <span class="sr-only">{{ title }}</span>
            </button>
        </h3>
        {% if hasContent %}
            <div class="slider-item__content" id="{{ id }}">
                <h4 class="slider-item__title">
                    {% if category %}
                        <span class="theme slider-item__category">{{ category }}</span>
                        <span class="sr-only"> : </span>
                    {% endif %}
                    {% if title %}
                        {% call wrapper(
                            className = 'slider-item__title-link',
                            tag = linkTag,
                            href = linkHref,
                            attrs = {}
                        ) %}
                            <span class="underline">{{ title }}</span>
                        {% endcall %}
                    {% endif %}
                </h4>
            </div>
        {% endif %}
    </article>
{% endmacro %}

{#
    HeroSlider template.
#}

{% macro HeroSlider() %}
    <div class="hero-slider js-hero-slider">
        <ul class="hero-slider__list">
            {% for item in range(4) %}
                <li class="hero-slider__item {{ 'is-focused' if loop.index === 1 }}">
                    {{ SliderItem(
                        linkTag = 'span' if loop.first else '',
                        linkHref = '' if loop.first else '#'
                    ) }}
                </li>
            {% endfor %}
        </ul>
    </div>
{% endmacro %}

{#
    HeroHome template.
#}
{%- macro HeroHome(type, hasVideo, count = 3, hideSliderArrows = false, hasCaption = false, hasContent=true, hasInlineBg = true) -%}
    <section class="hero is-{{ type }} {{ 'has-video' if hasVideo }}">
        <div class="hero__container">
            <h2 class="ghost">Mise en avant</h2>
            {%- if type === 'type-1' -%}
                {{ HeroItem(
                    hasSearch = true,
                    hideImageLink = false,
                    hasVideo = hasVideo,
                    hasCaption = false, hasContent=true, hasInlineBg = true
                ) }}
            {%- elif type === 'type-2' -%}
                {{ HeroItem(
                    category = false,
                    imageSizes = ['480x140?479', '768x140?767', '1280x298?1279', '1920x744'],
                    hasSearch = true,
                    hasContent = true,
                    hasVideo = hasVideo,
                    hasCaption = true
                ) }}
            {%- elif type === 'type-3' and not hasVideo -%}
                {{ HeroCarousel(count, hideArrows = hideSliderArrows) }}
            {%- elif type === 'type-3' and hasVideo -%}
                {{ HeroItem(
                    hasContent = true,
                    hasVideo = hasVideo
                ) }}
            {%- elif type === 'type-4' -%}
                {{ HeroItem(
                    category = false,
                    imageSizes = ['480x140?479', '768x140?767', '1280x298?1279', '1920x744'],
                    hasContent = true,
                    hasVideo = hasVideo,
                    hasCaption = true
                ) }}
            {%- elif type === 'type-5' and not hasVideo -%}
                {{ HeroCarousel(count, hideArrows = hideSliderArrows) }}
            {%- elif type === 'type-5' and hasVideo -%}
                {{ HeroItem(
                    hasContent = true,
                    hasVideo = hasVideo
                ) }}
            {%- elif type === 'type-6' and not hasVideo -%}
                {{ HeroCarousel(count, hasInlineBg = true, hideArrows = hideSliderArrows) }}
            {%- elif type === 'type-6' and hasVideo -%}
                {{ HeroItem(
                    hasContent = true,
                    hasVideo = hasVideo,
                    hasInlineBg = true
                ) }}
            {%- elif type === 'type-7' and not hasVideo %}
                {{ HeroSlider() }}
            {%- elif type === 'type-7' and hasVideo %}
                {{ HeroItem(
                    category = false,
                    imageSizes = ['480x140?479', '768x140?767', '1280x298?1279', '1920x744'],
                    hasContent = true,
                    hasVideo = hasVideo
                ) }}
            {%- endif -%}
        </div>
    </section>
{%- endmacro -%}

{#
    HeroHome template.
#}
{%- macro HeroHomeCareOffer(hasVideo, count = 3, hideSliderArrows = false) -%}
    <section class="hero is-type-3 {{ 'has-video' if hasVideo }}">
        <div class="hero__container">
            <h2 class="ghost">Mise en avant</h2>
            {% if hasVideo %}
                {{ HeroItem(category = fale, hasContent = true, hasVideo = hasVideo, hideSliderArrows = false) }}
            {% else %}
                {{ HeroCarousel(count, hideArrows = hideSliderArrows, category = fale, hasCaption = false, hasContent=true, hasInlineBg = true) }}
            {% endif %}
            {{ SearchServices(consultation = false) }}
        </div>
    </section>
{%- endmacro -%}
