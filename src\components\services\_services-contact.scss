.services-contact {
    $this: &;

    margin-bottom: 85px;
    
    .medical-team {
        display: flex;
        flex-wrap: wrap;
        margin-left: 67px;
        margin-top: 111px;
        max-width: 1070px;
        padding-bottom: 15px;
        padding-top: 35px;
        position: relative;

        @include breakpoint(medium down) {
            margin-left: 0;
            max-width: 100%;
        }

        @include breakpoint(small down) {
            margin-top: 30px;
        }

        &::before {
            @include absolute(0, null, null, 50%);
            @include size(100vw, 100%);
            background-color: $color-3--1;
            content: '';
            transform: translate(-50%);
            z-index: -1;
        }

        #{$this} {
            &__wrapper {
                flex-direction: column-reverse;
                margin-bottom: 42px;
                margin-right: 179px;
                width: 232px;

                @include breakpoint(medium down) {
                    margin-right: auto;
                    width: 50%;
                }

                @include breakpoint(small down) {
                    width: 100%;
                }

                &:nth-of-type(3n) {
                    @include breakpoint(large only) {
                        margin-right: 0;
                    }
                }
            }

            &__picture {
                margin: 0 auto 9px;
            }

            &__name {
                text-align: center;
            }

            &__name-wrapper {
                text-align: center;
            }
        }
    }
    
    &__list {
        background-color: $color-3--1;
        margin-left: 0 !important;
        padding: 70px !important;

        @include breakpoint(medium down) {
            padding: 20px !important;
        }
    }

    &__wrapper {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;

        @include breakpoint(small down) {
            align-items: center;
            flex-direction: column-reverse;
            padding: 0;
            text-align: center;
        }
    }

    &__picture {
        display: block;
        flex-grow: 0;
        flex-shrink: 0;
        margin: 0 42px 0 0;

        @include breakpoint(small down) {
            margin: 0 auto 20px;
        }
    }

    &__name-wrapper {
        padding-top: 2px;

        .btn {
            border-color: var(--color-1--5) !important;
            color: var(--color-1--5) !important;
            margin-top: 21px;
            padding: 0 29px !important;
            text-decoration: none !important;

            @include on-event() {
                background-color: var(--color-1--1) !important;
                border-color: var(--color-1--1) !important;
                color: $color-white !important;
            }
        }
    }

    &__name {
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        flex-direction: column;
        line-height: 2.4rem;
        margin: 0 !important;
    }
    
    &__name-title,
    &__name-first,
    &__name-last {
        @include font(var(--typo-1), 2rem !important, var(--fw-bold));
        color: $color-black;
    }

    &__name-position {
        @include font(var(--typo-1), 1.8rem !important, var(--fw-bold));
        color: var(--color-1--5);
        display: block;
        line-height: 2.2rem;
        margin-top: 9px;
        width: 100%;
    }
    
    &__name-specialite {
        @include font(var(--typo-1), 1.4rem !important, var(--fw-normal));
        color: var(--color-1--5);
        display: block;
        margin-top: 5px;
    }

    &__info-title {
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        color: $color-black;
        line-height: 2.4rem;
        margin: 0;
    }

    &__info {
        flex-shrink: 0;
        padding-top: 20px;
        width: 315px;

        @include breakpoint(medium down) {
            padding-top: 33px;
            width: 100%;
        }

        .btn {
            margin: 10px 0 0;

            @include breakpoint(medium down) {
                margin: 10px 10px 0 0;
            }
        }
    }

    &__listitem {
        align-items: center;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-left: 0 !important;

        @include breakpoint(medium down) {
            flex-wrap: wrap;
            justify-content: center;
        }

        &::before {
            content: none !important;
        }

        &:last-child {
            margin: 0;
        }
    }

    &__listitem-title {
        @include font(var(--typo-1), 1.6rem !important, var(--fw-normal));
        color: $color-black;
        display: inline-block;
        line-height: 2.2rem;
        margin: 0 30px 0 0;
        padding-right: 30px;
        position: relative;
        width: 100%;

        @include breakpoint(small down) {
            margin: 0 0 10px;
            padding: 0;
            text-align: center;
        }

        &::after {
            @include absolute(null, 0, 5px, null);
            @include size(100%, 3px);
            background-image: url("data:image/svg+xml,%3Csvg width='10' height='3' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='0' y='0' width='2' height='2'/%3E%3C/svg%3E");
            background-position: bottom right;
            content: '';
            display: block;

            @include breakpoint(small down) {
                content: none;
            }
        }

        span {
            background-color: $color-3--1;
            padding-right: 5px;
            position: relative;
            z-index: 1;
        }
    }

    &__listitem-link {
        flex-shrink: 0;

        @include breakpoint(medium down) {
            margin-top: 15px;
        }

        @include breakpoint(small down) {
            align-items: center;
            display: flex;
            flex-direction: column;
        }

        .btn {
            border-color: var(--color-1--5) !important;
            color: var(--color-1--5) !important;
            margin-right: 12px;
            min-height: auto !important;
            padding: 14px 30px !important;
            text-decoration: none !important;

            @include breakpoint(small down) {
                margin-bottom: 10px;
                margin-inline: auto;
            }
            
            &:last-of-type {
                margin-right: 0;
                
                @include breakpoint(small down) {
                    margin-inline: auto;
                }
            }

            @include on-event() {
                background-color: var(--color-1--1) !important;
                border-color: var(--color-1--1) !important;
                color: $color-white !important;
            }
        }
    }

    &__title {
        margin-top: 50px !important;
    }
}
