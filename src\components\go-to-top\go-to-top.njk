{%- from 'views/utils/utils.njk' import setAttributes -%}

{#
    GoToTop template.
#}
{%- macro GoToTop(
    icon = 'fa-regular fa-arrow-up-long',
    scrollTarget = '0',
    focusTarget = '.site-wrapper:global',
    bottomElement = '.footer__wrapper:global',
    hideAtTop = 'true'
) -%}
    <div class="go-to-top js-scroll-handler"
         {{ setAttributes({
             'data-scroll-target': scrollTarget,
             'data-focus-target': focusTarget,
             'data-bottom-element': bottomElement,
             'data-hide-at-top': hideAtTop
         }) }}
    >
            <a href="#" class="go-to-top__link">
                <span class="{{ icon }}" aria-hidden="true"></span>
            </a>
    </div>
{%- endmacro -%}
