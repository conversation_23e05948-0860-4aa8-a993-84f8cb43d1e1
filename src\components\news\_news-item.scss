.news-item {
    $this: &;

    @extend %link-block-context;
    display: flex;
    flex-direction: column-reverse;
    margin: 0 auto;

    @include breakpoint(medium down) {
        align-items: flex-start;
        display: flex;
        flex-direction: row-reverse;
    }

    &__image {
        @include size(282px,189px);
        display: block;
        position: relative;
        z-index: -1;
        
        @include breakpoint(medium down) {
            @include size(100px,67px);
            flex: none;
        }

        img {
            @include object-fit();
            @include size(100%);

            @include breakpoint(small down) {
                height: auto;
            }
        }
    }

    &__content {
        background-color: $color-white;
        padding: 19px 10px 5px 0;

        @include breakpoint(medium down) {
            flex-grow: 1;
            margin: 0;
            padding: 0 0 0 10px;
        }

        @include breakpoint(small down) {
            max-width: 68.5%;
            padding: 0 0 0 10px;
        }

        > *:last-child {
            margin-bottom: 0;

            &.item-title {
                font-size: 2rem;
                letter-spacing: 0;
                line-height: 23px;
            }
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__category {
        color: var(--color-1--5);
        line-height: 21px;
        margin-bottom: 3px;

        @include breakpoint(medium down) {
            margin-bottom: 5px;
        }

        &.theme {
            font-size: 1.4rem;
            letter-spacing: 2.52px;
            
            @include breakpoint(medium down) {
                font-size: 1.2rem;
                letter-spacing: 2.16px;
            }
        }
    }

    &__actions {
        @include absolute(-5px, -5px, null, null);
        z-index: 11;
    }

    .is-sitefactory & {
        margin: 0 0 40px;

        &__category {
            background-color: var(--color-1--1);
        }

        @include breakpoint(small down) {
            margin-top: 40px;
        }
    }

    // Widget 33%
    .is-width-33 & {
        align-items: center;
        
        #{$this}__content {
            @include breakpoint(large) {
                margin: 0 auto;
                max-width: calc(100% - 74px);
                padding: 10px 30px 0;
                text-align: center;

                &::after {
                    left: 50%;
                    transform: translate(-50%, calc(50px - 100%));
                }
            }
        }
    }

    // Widget 100% on page content and home
    .news-home &,
    .news-content & {
        #{$this}__content {
            @include breakpoint(large) {
                padding: 19px 1px 0 0;
            }
        }
    }
}
