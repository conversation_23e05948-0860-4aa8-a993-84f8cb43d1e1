{%- from 'views/utils/utils.njk' import setAttr -%}
{%- from 'views/core-components/dropdown.njk' import Dropdown -%}

{#
    Breadcrumbs template.
    @params {string[]} - breadcrumbs chain links
#}
{%- macro Breadcrumbs(
    pageTitle = 'Dans cette rubrique',
    dropdown = true,
    links = [
        'Lorem ipsum',
        'Consectur',
        pageTitle or 'Dans cette rubrique'
    ]
) -%}
    <nav class="breadcrumbs" aria-label="Vous êtes ici" role="navigation">
        <ol class="breadcrumbs__list">
            <li class="breadcrumbs__item">
                <a href="#" class="js-tooltip" data-content="Accueil">
                    <span class="far fa-regular fa-house-chimney" title="Accueil" aria-hidden="true"></span>
                    <span class="sr-only">Accueil</span>
                </a>
            </li>
            {% if links.length > 1 %}
                <li class="breadcrumbs__item is-toggle">
                    <button
                        type="button"
                        aria-label="Déployer toute l'arborescence"
                        aria-expanded="true"
                        data-content="Déployer toute l'arborescence"
                        class="breadcrumbs__items-toggle js-breadcrumbs-items-toggle js-tooltip"
                    >...</button>
                </li>
            {% endif %}
            {% for link in links %}
                {% if loop.last and dropdown %}
                    <li class="breadcrumbs__item">
                        {% call Dropdown({
                            wrapper: {
                                className: 'breadcrumbs-nav'
                            },
                            toggle: {
                                customClassName: 'js-tooltip',
                                tooltip: link + ' (pages de même niveau)',
                                hiddenText: 'Pages de même niveau',
                                text: link
                            },
                            attrs: {
                                'aria-current': 'page'
                            }
                        }) %}
                            <nav class="breadcrumbs-nav__menu" role="navigation" aria-label="{{ link }}, pages de même niveau">
                                <ul class="breadcrumbs-nav__menu-links">
                                    {% for link in range(10) %}
                                        <li class="breadcrumbs-nav__item">
                                            <a href="#" class="breadcrumbs-nav__link">{{ lorem(3, 'words') }}</a>
                                        </li>
                                    {% endfor %}
                                </ul>
                            </nav>
                        {% endcall %}
                    </li>
                {% elseif loop.last and not dropdown %}
                    <li class="breadcrumbs__item">
                        <span class="breadcrumbs__link">{{ link }}</span>
                    </li>
                {% else %}
                    <li class="breadcrumbs__item" {{ setAttr('aria-hidden', 'false') }}>
                        <a href="#" class="breadcrumbs__link">{{ link }}</a>
                    </li>
                {% endif %}
            {% endfor %}
        </ol>
    </nav>
{%- endmacro -%}
