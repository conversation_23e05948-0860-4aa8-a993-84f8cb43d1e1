.quicklink-item {
    $this: &;

    @extend %link-block-context;
    align-items: center;
    display: flex;
    flex-direction: column;
    padding: 5px;

    @include breakpoint(medium down) {
        padding: 3px;
    }

    @include breakpoint(small down) {
        flex-direction: row;
        justify-content: center;
    }

    &__svg-wrapper {
        @include size(60px);
        flex-shrink: 0;

        @include breakpoint(small down) {
            @include size(36px);
            margin-right: 15px;
        }

        svg {
            @include size(100%);
            fill: var(--color-1--5);
        }
    }

    &__text {
        @extend %link-block;
        @extend %underline-context;
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: var(--color-1--5);
        line-height: calc(18 / 14);
        margin: 20px 0 0;
        text-align: center;

        @include breakpoint(small down) {
            margin: 0;
        }

        .underline {
            @include multiline-underline();
        }

        &:focus-visible {
            &::after {
                outline-offset: -2px;
            }
        }
    }

    .is-inverted & {
        #{$this}__svg-wrapper {
            svg {
                fill: $color-white;
            }
        }

        #{$this}__text {
            color: $color-white;

            .underline {
                @include multiline-underline();
            }
        }
    }
}
