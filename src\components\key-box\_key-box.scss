.key-box-item {
    align-items: center;
    display: flex;
    flex-direction: column;

    @include breakpoint(medium down) {
        margin: 0 auto;
        max-width: 768px;
    }

    @include breakpoint(small down) {
        margin-bottom: 30px;
    }

    &__icon {
        @include size(145px);
        display: flex;
        justify-content: center;
        margin: 0 auto;

        svg {
            @include size(100%);
            fill: var(--color-1--5);
        }
    }

    &__title-wrapper {
        background-color: var(--color-2--1);
        margin: 25px 0;
        padding: 4px 30px;
    }

    &__title {
        color: $color-black;
        font-size: 4.5rem;
        font-weight: var(--fw-bold);
        line-height: 5rem;

        strong {
            font-weight: inherit;
        }
    }

    &__description {
        font-size: 2rem;
        line-height: 3.4rem;
        text-align: center;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
        }
    }
}
