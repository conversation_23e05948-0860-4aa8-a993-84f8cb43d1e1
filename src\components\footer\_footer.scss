// Footer styles

.footer {
    &__wrapper {
        background-color: $color-3--1;
        margin-bottom: -1px;
        overflow: hidden;
        position: relative;
    }

    &__container {
        @extend %container;
        padding-bottom: 0;
        padding-top: 48px;

        @include breakpoint(medium down) {
            padding: 62px 0 0;
        }

        @include breakpoint(small down) {
            padding-top: 50px;
        }
    }

    &__row {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        @include breakpoint(medium down) {
            flex-wrap: wrap;
        }

        @include breakpoint(small down) {
            display: block;
        }
    }

    &__column {
        @include breakpoint(medium down) {
            margin: 0 auto;
            max-width: 768px;
        }

        @include breakpoint(small down) {
            max-width: 320px;
        }

        &.is-first {
            flex-grow: 1;
            width: 1%;

            @include breakpoint(medium down) {
                padding: 0 10px;
                width: 100%;
            }
        }

        &.is-second {
            padding: 15px 0;
            position: relative;
            width: 100%;

            @include breakpoint(medium down) {
                padding: 20px 0;
            }

            @include breakpoint(small down) {
                padding: 0;
            }
        }
    }

    &__bottom {
        align-items: center;
        background-color: $color-3--1;
        display: flex;

        @include breakpoint(medium down) {
            flex-direction: column;
            padding: 0 0 22px;
        }

        @include breakpoint(small down) {
            padding: 27px 0 40px;
        }
    }

    &__certification-image {
        @include size(81px, 113px);
        display: block;

        img {
            border-radius: 0 7px 7px 7px;
            height: 100%;
            object-fit: cover;
        }
    }

    &__rgaa {
        @include trs;
        color: $color-black;
        display: block;
        flex-shrink: 0;
        font-size: 14px;
        line-height: 0.5;
        min-width: 142px;
        padding: 18px 0 24px 45px;
        text-decoration: none;

        @include breakpoint(medium down) {
            @include absolute(null, null, 10px, 10px);
            min-width: unset;
            padding: 0;
            width: initial;
        }

        &[href] {
            @include on-event {
                color: var(--color-1--1);
            }
        }

        @include add-inverted-styles {
            color: $color-white;

            &[href] {
                @include on-event {
                    color: var(--color-2--1);
                }
            }
        }
    }

    &__rgaa-caption {
        font-size: 11px;
        line-height: 1;
    }

    &__rgaa-status {
        font-weight: bold;
    }

    &__rgaa-status-wrapper {
        display: block;
        margin-top: 1px;
    }

    //name Stratis signature
    &__stratis {
        @include font(var(--typo-1), 1.1rem, var(--fw-bold));
        background-color: transparent;
        color: $color-white;
        flex-shrink: 0;
        line-height: 1.6rem;
        margin: 0;
        text-align: right;
        text-transform: uppercase;
        z-index: 5;

        @include breakpoint(medium down) {
            @include absolute(null, 0, 10px, null);
        }

        a {
            @include trs;
            background: var(--color-1--5);
            color: $color-white;
            display: inline-block;
            padding: 3px 14px;
            text-decoration: none;

            &:hover,
            &:focus {
                background: $color-white;
                color: var(--color-1--1);
            }
        }
    }

    @include add-inverted-styles {
        &__wrapper {
            background-color: var(--color-1--2);
        }

        &__bottom {
            background-color: var(--color-1--2);
        }
    }
}
