//genial decor for home style
@mixin home-styled-decor {
    &::before {
        @include absolute(0, null, null, 50%);
        @include size(1000vw, calc(100% - 111px));
        background-color: $color-white;
        content: '';
        transform: translateX(-500vw);
        z-index: -1;

        @include breakpoint(medium down) {
            background-color: var(--color-1--2);
            height: 100%;
        }

        @include breakpoint(small down) {
            height: calc(100% + 20px);
            top: -20px;
        }
    }
}

.event-focus {
    $this: &;
    padding-bottom: 60px;
    position: relative;

    @include breakpoint(medium down) {
        padding-bottom: 20px;
    }

    // styles for HP with background
    .events-home & {
        &::before {
            @include absolute(null, null, 0, 50%);
            @include size(100vw, 100%);
            background-color: var(--color-1--2);
            content: '';
            transform: translateX(-50%);

            @include breakpoint(medium down) {
                height: 20%;
            }
        }

        #{$this} {
            &__text {
                @include home-styled-decor();
                @include breakpoint(medium down) {
                    background-color: var(--color-1--2);
                }
            }

            &__category {
                @include breakpoint(medium down) {
                    color: var(--color-2--1);
                }
            }

            &__title {
                @include breakpoint(medium down) {
                    color: $color-white;
                }

                .underline {
                    @include breakpoint(medium down) {
                        @include multiline-underline();
                    }
                }
            }

            &__time-place {
                .time-place__item {
                    @include breakpoint(medium down) {
                        color: var(--color-1--3);
                    }
                }
            }
        }
    }

    &__wrapper {
        @extend %link-block-context;
        display: flex;
        flex-direction: row-reverse;
        position: relative;
        z-index: 1;

        @include breakpoint(medium down) {
            flex-direction: column-reverse;
        }

        @include breakpoint(small down) {
            padding: 0;
        }

        @include on-event() {
            #{$this}__title-link {
                .underline {
                    background-size: 100% 100%;
                }
            }
        }
    }

    &__picture-link {
        flex-shrink: 0;
        overflow: hidden;
        width: 996px;
        z-index: -1;

        @include breakpoint(medium down) {
            width: 100%;
        }
    }

    &__picture {
        img {
            @include object-fit();
            @include size(100%);
        }
    }

    &__content {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        height: 100%;
        margin-left: -240px;
        position: relative;
        word-break: break-word;

        @include breakpoint(medium down) {
            flex-direction: row;
            margin: -108px 0 0;
        }

        @include breakpoint(small down) {
            flex-direction: column-reverse;
            margin: 0;
        }
    }

    &__date {
        @include breakpoint(medium down) {
            margin: 108px -36px 0 0;
        }

        @include breakpoint(small down) {
            margin: -20px 0 0 -20px;
        }
    }

    &__text {
        background-color: $color-white;
        padding: 40px 40px 52px;

        @include breakpoint(medium down) {
            min-height: 108px;
            padding: 38px 40px 20px 0;
            width: 384px;
        }

        @include breakpoint(small down) {
            padding: 18px 0;
            width: auto;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__category {
        @include font(null, 1.4rem, var(--fw-medium));
        color: var(--color-1--1);
        margin-top: 8px;
        z-index: 2;
    }

    &__time-place {
        display: flex;
        margin-top: 20px;

        .time-place__item {
            margin-right: 16px;

            &.is-time {
                flex-shrink: 0;
            }

            @include breakpoint(large only) {
                font-size: 1.2rem;
                font-weight: var(--fw-medium);
            }
        }
    }

    &__actions {
        @include absolute(35px, 45px);
        z-index: 11;
    }
}
