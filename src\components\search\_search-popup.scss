.search-popup {
    $this: &;

    &.fancybox-content {
        max-width: 780px;
        padding: 0;
        width: 100%;
    }

    &__wrapper {
        @include breakpoint(medium only) {
            padding: 0 10px;
        }

        .is-visible {
            .form__field {
                color: $color-black;
            }
        }
    }

    .form {
        &__controls-group {
            align-items: center;
            display: flex;
            flex-wrap: wrap;
            margin: 0;
        }

        &__label {
            @include font(var(--typo-1), 4.5rem, var(--fw-bold));
            color: $color-white;
            line-height: 1.25;
            margin: 0 0 27px;
            text-align: center;
            width: 100%;

            @include breakpoint(medium down) {
                font-size: 3.5rem;
            }

            @include breakpoint(small down) {
                font-size: 2.6rem;
            }
        }

        &__field-wrapper {
            flex-grow: 1;
            margin: 0;
            width: 1%;

            &.js-autocomplete {
                .js-autocomplete-input-clear {
                    right: 20px;

                    @include breakpoint(small down) {
                        right: 10px;
                    }
                }

                .js-autocomplete-result-wrapper {
                    box-shadow: none;
                    height: 0;
                }

                &.is-visible {
                    .form__field {
                        color: $color-black;
                    }

                    .js-autocomplete-result-wrapper {
                        height: auto;
                    }
                }
            }
        }

        &__field {
            background-color: $color-white;
            border-color: $color-white;
            color: $color-3--4;
            font-size: 1.8rem;
            font-weight: 400;
            height: 80px;

            @include breakpoint(medium down) {
                height: 63px;
            }

            @include breakpoint(small down) {
                font-size: 1.6rem;
            }

            &::placeholder {
                color: inherit;
                font-size: 1.8rem;
                font-weight: 400;
            }

            &::-ms-clear {
                display: none;
            }

            &:focus ~ .form__field-placeholder,
            &:not(:focus):valid ~ .form__field-placeholder {
                color: $color-white;
                left: 0;
                top: -20px;

                @include breakpoint(small down) {
                    top: -12px;
                }
            }
        }

        &__field-placeholder {
            @include trs;
            @include absolute(50%, 55px, null, 30px);
            @include font(var(--typo-1), 2rem, var(--fw-normal));
            color: $color-3--4;
            font-size: 1.8rem;
            overflow: hidden;
            pointer-events: none;
            text-overflow: ellipsis;
            transform: translateY(-50%);
            white-space: nowrap;

            @include breakpoint(small down) {
                font-size: 1.4rem;
                left: 15px;
                right: 45px;
            }
        }
    }

    &__submit {
        @extend %button;
        @extend %button-style-only-icon;
        @extend %button-style-primary;
        background-color: var(--color-1--5);
        border-color: var(--color-1--5);
        margin-left: 10px;

        @include breakpoint(large) {
            @include size(80px);
        }

        @include fa-icon-style(false) {
            font-size: 1.4rem;
        }
    }

    &__tags {
        margin: 46.5px auto 0;
        max-width: 506px;
    }
}
