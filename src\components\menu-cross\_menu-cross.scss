.menu-cross {
    $this: &;

    &__link {
        @include font(var(--typo-1), 1.2rem, var(--fw-normal), normal);
        @include trs;
        @include focus-outline($offset: 2px);
        background-color: transparent;
        border: 0;
        color: $color-black;
        cursor: pointer;
        display: flex;
        letter-spacing: 1.04px;
        padding: 0;
        position: relative;
        text: {
            align: center;
            decoration: none;
            transform: uppercase;
        }
        width: 100%;

        &[aria-current="page"],
        &:hover,
        &:focus {
            color: var(--color-1--1);

            #{$this}__text {
                text-decoration: underline;
            }
        }

        @include breakpoint(small down) {
            font-size: 1.3rem;
        }

        @include add-inverted-styles {
            color: $color-white;
        }
    }

    &__wrapper {
        @include breakpoint(large only) {
            margin: 0 auto;
            max-width: 970px;
            padding: 0 40px;
        }
    }

    &__list[class] {
        display: flex;
        flex-wrap: wrap;
        margin-left: 0;

        @include breakpoint(medium down) {
            justify-content: center;
            margin-inline: auto;
            margin-top: 18px;
            padding-left: 5px;
            width: 506px;
        }

        @include breakpoint(small down) {
            flex-direction: row;
            flex-wrap: wrap;
            margin-bottom: 6px;
            margin-top: 8px;
            padding-left: 0;
            width: 320px;
        }
    }

    &__item {
        margin: 5px 9px 5px 0;
        padding: 0 14px;

        @include breakpoint(medium down) {
            margin-right: 0;
        }

        @include breakpoint(small down) {
            margin: 0 0 9px;
        }
    }

    &__text {
        color: inherit;
        flex-grow: 1;
        font: inherit;
    }
}
