.search-services {
    $this: &;

    margin: 0 63px 0 0;
    position: relative;
    width: 352px;
    z-index: 99;

    @include breakpoint(medium down) {
        margin: 37px 0 0 24px;
        width: 158px;
    }

    @include breakpoint(small down) {
        margin-left: 20px;
        margin-top: 69px;
        width: 320px;
    }

    &.is-opened {
        #{$this}__title {
            color: $color-black;

            svg {
                fill: var(--color-1--1);
            }
        }

        #{$this}__wrap {
            background-color: $color-white;
        }

        #{$this}__list-item {
            border-color: $color-3--4;
        }

        #{$this}__list-toggle {
            color: $color-black;

            &::before {
                background-color: $color-3--4;
            }

            &.is-open,
            &:hover,
            &:focus {
                color: var(--color-1--1);

                #{$this}__list-toggle-text {
                    &::after {
                        background-color: var(--color-1--1);
                        width: 100%;
                    }
                }
            }
        }
    }

    &__container {
        @extend %container;
        max-width: 1200px;
        padding: 0;
        position: relative;

        @include breakpoint(medium down) {
            // padding: 0 62px;
        }

        @include breakpoint(small down) {
            padding: 0;
        }
    }

    &__wrap {
        @include trs($duration: 100ms);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        padding: 0;
        position: relative;

        @include breakpoint(medium down) {
            flex-direction: column;
        }

        @include breakpoint(small down) {
            padding: 0;
        }
    }

    &__title {
        @include trs;
        @include font(var(--typo-1), 3.5rem, var(--fw-bold));
        align-items: center;
        color: var(--color-1--2);
        line-height: 4rem;
        margin-bottom: 33px;
        position: relative;

        @include breakpoint(medium down) {
            font-size: 2.8rem;
            margin: 0 0 33px;
        }

        @include breakpoint(small down) {
            display: block;
            font-size: 3.5rem;
        }

        &::after {
            @include size(78px, 4px);
            @include absolute(null, null, -11px, 2px);
            background-color: var(--color-2--4);
            content: "";

            @include breakpoint(medium down) {
                bottom: -10px;
                left: 1px;
            }

            @include breakpoint(small down) {
                bottom: -13px;
                left: 0;
            }
        }

        svg {
            @include trs;
            @include size(60px, 66px);
            display: block;
            fill: $color-white;
            flex-shrink: 0;
            margin-right: 15px;

            @include breakpoint(medium down) {
                @include size(46px, 42px);
            }

            @include breakpoint(small down) {
                margin: 0 auto 5px;
            }
        }
    }

    & &__list {
        display: flex;
        flex-shrink: 0;
        margin: 0;

        @include breakpoint(medium down) {
            flex-direction: column;
        }

        @include breakpoint(small down) {
            align-items: center;
            flex-direction: row;
            width: 100%;
        }
    }

    &__list-item {
        align-items: center;
        display: flex;
        padding: 0;
        position: static;

        @include breakpoint(small down) {
            border: 0;
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
            margin-left: 39px;
            max-width: 520px;
            padding: 0;
        }

        &:first-child {
            border: 0;
            margin-right: 61px;

            #{$this}__list-toggle:before {
                content: none;
            }

            #{$this}__list-toggle-text {
                @include breakpoint(small down) {
                    padding-right: 25px;
                }
            }
            
            @include breakpoint(medium down) {
                margin-right: 0;
            }
            
            @include breakpoint(small down) {
                margin-left: 0;
            }
        }

        &.is-open {
            #{$this}__panel {
                display: block;
            }
        }
    }

    &__list-toggle {
        @include focus-outline($offset: 3px);
        @include font(var(--typo-1), 2rem, var(--fw-medium));
        align-items: center;
        background: transparent;
        border: 0;
        border-radius: 0;
        color: var(--color-1--5);
        cursor: pointer;
        display: flex;
        min-height: 30px;
        padding: 0;
        position: relative;

        span[class*="fa-"] {
            font-size: 1.2rem;
        }

        @include on-event {
            #{$this}__list-toggle-text {
                &::after {
                    width: 100%;
                }
            }
        }
    }

    &__list-toggle-text {
        @include trs;
        display: block;
        margin-right: 1px;
        padding-right: 18px;
        position: relative;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
            line-height: 34px;
            padding-right: 27px;
        }

        &::after {
            @include absolute(null, null, -2px, 50%);
            @include trs;
            @include size(0, 4px);
            background-color: $color-white;
            content: "";
            transform: translateX(-50%);
            z-index: 11;
        }
    }

    &__panel {
        @include absolute(100%, 0, null, 0);
        background-color: $color-white;
        box-shadow: 0 25px 25px rgba(0, 0, 0, 0.16);
        display: none;
        padding: 30px 50px 50px;
        width: 700px;
        z-index: 10;

        @include breakpoint(small down) {
            box-shadow: none;
            left: -20px;
            padding: 40px 30px 30px;
            top: 100px;
            width: 100vw;
        }
    }

    & &__close {
        @include absolute(25px, 40px);
        border: 0;
        z-index: 1;

        @include breakpoint(medium down) {
            right: 20px;
        }

        @include breakpoint(small down) {
            right: 15px;
            top: 32px;
        }

        @include fa-icon-style(false) {
            font-size: 3rem;
        }
    }

    .form {
        margin: 0;

        &__legend {
            @include font(var(--typo-1), 2.8rem, var(--fw-black));
            line-height: 3.2rem;
            margin-bottom: 40px;
            padding-right: 40px;

            @include breakpoint(small down) {
                font-size: 2.4rem;
                line-height: 2.6rem;
            }
        }

        &__field-wrapper {
            margin: 0 0 25px;

            @include breakpoint(medium down) {
                align-items: flex-start;
                flex-direction: column;
                margin: 0 0 20px;
            }
        }

        &__actions {
            margin: 15px 0 0;

            @include breakpoint(medium down) {
                margin-top: 20px;
            }

            button {
                margin: 0;
            }
        }
    }
}
