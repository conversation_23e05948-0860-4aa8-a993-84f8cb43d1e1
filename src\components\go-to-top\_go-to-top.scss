.go-to-top {
    @include size(45px);
    @include fixed(null, 21px, 0, null);
    @include font(null, 1.2rem, var(--fw-normal));
    @include trs;
    align-items: center;
    background-color: var(--color-2--1);
    color: var(--color-1--1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 1;
    text-decoration: none;
    visibility: visible;
    z-index: 19;

    @include breakpoint(medium only) {
        right: 44px;
    }

    @include on-event {
        background-color: var(--color-2--2);
        color: $color-black;
    }

    &.is-hidden {
        display: none;
        opacity: 0;
        visibility: hidden;
    }

    span[class*="fa-"] {
        font-size: 1.2rem;
        margin-top: 5px;
    }
}
