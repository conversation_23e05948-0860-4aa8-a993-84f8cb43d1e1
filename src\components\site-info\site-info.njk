{%- from 'views/utils/utils.njk' import wrapper, getImagePath -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'components/social/social.njk' import SocialLinks -%}
{%- from 'views/core-components/image.njk' import Image -%}

{#
    SiteInfo template.
    @param {boolean} isHome - check if isHome page and remove link from logo
    @param {boolean} otherInfo - insert other info block
#}
{%- macro SiteInfo(
    isHome = false,
    isInverted = false,
    link = '' if isHome else kGlobalLinks.home
) -%}
    <section class="site-info">
        <h2 class="ghost">Informations du site</h2>
        <div class="site-info__column">
            <div class="site-info__image">
                {% call wrapper(className ='logo', href = '' if isHome else './index.html') %}
                    {% if isInverted %}
                        <img src="{{ getImagePath('logos/logo-footer-inverted.svg') }}" width="242" height="64" alt="{{ '' if isHome else '[CLIENT\'S NAME] (retour à l\'accueil)' }}">
                    {% else %}
                        <img src="{{ getImagePath('logos/logo-footer.svg') }}" width="242" height="64" alt="{{ '' if isHome else '[CLIENT\'S NAME] (retour à l\'accueil)' }}">
                    {% endif %}
                {% endcall %}
            </div>
            <address class="site-info__content">
                <p class="site-info__item is-name">CHI Brignoles Le Luc</p>
                <p class="site-info__item is-address">
                    <span class="ghost">Adresse:</span>
                    <span>{{"Adresse juridique : <br/> 95 Rue Joseph Monnier, 83170 Brignoles" | safe}}</span>
                </p>
                <p class="site-info__item is-phone">
                    <span class="ghost">Tél.:</span>
                    <a href="tel:0494726600" title="Téléphone : 04 94 72 66 00" aria-label="Téléphone : 04 94 72 66 00">Téléphone : 04 94 72 66 00</a>
                </p>
            </address>
        </div>
        <div class="site-info__column">
            <ul class="site-info__list">
                <li class="site-info__list-item">
                    {{ Link(
                        text = 'Nous contacter',
                        className = 'btn is-small',
                        icon = false
                    ) }}
                </li>
                <li class="site-info__list-item">
                    {{ Link(
                        text = 'infos pratiques',
                        className = 'btn is-small',
                        icon = false
                    ) }}
                </li>
            </ul>
            {{ SocialLinks(links=[{title: 'Accéder à la page Facebook', icon: 'fab fa-facebook-f'}, {title: 'Accéder à la page LinkedIn', icon: 'fab fa-linkedin-in'}]) }}
        </div>
        <div class="site-info__column">
            <a href="{link}">
                {{ Image({className: 'footer__certification-image', sizes: ['81x113'], alt: 'image alt'}) }}
            </a>
        </div>
    </section>
{%- endmacro -%}



