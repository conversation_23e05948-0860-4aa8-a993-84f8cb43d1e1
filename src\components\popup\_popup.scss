/*
 * @name fancybox.
 */

.fancybox__backdrop {
    background: var(--color-1--6) !important;
}

.fancybox__caption {
    background: transparent !important;
    color: $color-white !important;
}

.fancybox-slide {
    $this: &;

    padding: 50px 20px !important;
    scroll-behavior: smooth;

    @include breakpoint(small down) {
        padding: 70px 20px !important;
    }

    &#{$this}--image {
        .fancybox-image {
            left: 50%;
            max-width: 100%;
            transform: translateX(-50%);
            width: auto;
        }
    }
}

.fancybox__content {
    background: transparent;
    margin: 0 !important;
    overflow: visible;
    padding: 0;

    &.is-transparent {
        background: transparent !important;
    }
}

.with-fancybox {
    overflow: hidden;

    .site-wrapper {
        filter: blur(15px);
    }
}

.fancybox-slide--iframe {
    padding: 100px 0 !important;

    @include breakpoint(medium down) {
        padding: 85px 0 !important;
    }

    @include breakpoint(small down) {
        padding: 70px 20px !important;
    }

    .fancybox__content {
        height: 100% !important;
        width: 100% !important;
    }
}

/*
 * @name popup.
 */
.popup {
    $this: &;
    position: initial;

    &__close-btn {
        &.is-filters-close-btn {
            @include font(null, 3rem, var(--fw-normal));
            @include size(40px);
            @include trs();
            align-items: center;
            background: none;
            border: 0;
            border-radius: 4px;
            color: var(--color-1--1);
            cursor: pointer;
            display: flex;
            flex-shrink: 0;
            justify-content: center;
            text-decoration: none;

            @include fa-icon-style {
                color: inherit;
            }

            @include on-event {
                background-color: var(--color-1--1);
                color: $color-white;
            }
        }

        &.is-outside {
            @include absolute(50px, 50px, null, null);

            @include breakpoint(medium down) {
                right: 30px;
                top: 30px;
            }

            @include breakpoint(small down) {
                right: 20px;
                top: 20px;
            }
        }
    }

    &.is-filters {
        background: $color-white;
        max-width: 1200px;
        padding: 50px;
        position: relative;
        width: 100%;

        @include breakpoint(medium down) {
            max-width: 644px;
        }

        @include breakpoint(small down) {
            padding: 40px 20px;
        }

        #{$this}__close-btn {
            @include absolute(45px, 40px, null, null);

            @include breakpoint(small down) {
                right: 10px;
                top: 35px;
            }
        }
    }

    &.is-legend-helper {
        background-color: $color-white;
        max-width: 600px;
        padding: 50px;
        width: 100%;
    }
}

.f-button.is-close-btn {
    @include size(40px);
    align-items: center;
    border-radius: 0;
    display: none;
    justify-content: center;
}

button[data-fancybox-close] {
    &:focus {
        outline: 2px solid currentColor !important;
    }
}

.fancybox__slide.has-iframe,
.has-youtube .fancybox__content,
.has-vimeo .fancybox__content,
.has-html5video .fancybox__content {
    height: 100% !important;
    padding: 50px 20px !important;
    width: 100% !important;
}
