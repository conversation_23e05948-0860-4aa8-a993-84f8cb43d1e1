{%- from 'views/utils/constants.njk' import kGlobalLinks -%}
{%- from 'views/utils/utils.njk' import setAttr, wrapper -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/widget.njk' import Widget -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'components/share/share.njk' import Share -%}
{%- from 'views/core-components/secondary.njk' import TagLinks -%}
{%- from 'components/reviews/reviews.njk' import ReviewsHome -%}

{% set defaultSettingsItem = {
    title: lorem(1),
    teaser: false,
    category: lorem(range(1, 3) | random, 'words'),
    imageSizes: ['384x256'],
    tag: 'h3',
    role: false,
    ariaLevel: false,
    useShare: false
} %}

{#
    NewsItem template.
    @param {object} settings - user settings object
#}
{%- macro NewsItem(settings = {}) -%}
    {# Default params  #}
    {% set params = Helpers.merge(defaultSettingsItem, settings) %}

    <article class="news-item">
        <div class="news-item__content">
            {% if params.title %}
                <{{ params.tag }}
                    class="item-title news-item__title"
                    {{ setAttr('role', params.role) }}
                    {{ setAttr('aria-level', params.ariaLevel) }}
                >
                {% if params.category %}
                    <span class="theme news-item__category">{{ params.category }}</span>
                    <span class="sr-only"> : </span>
                {% endif %}
                    <a href="{{ kGlobalLinks.singleNews }}" class="news-item__title-link">
                        {% if params.useInlineBgTitle %}
                            <span class="js-inline-bg underline" data-max-length="20">{{ params.title }}</span>
                        {% else %}
                            <span class="underline">{{ params.title }}</span>
                        {% endif %}
                    </a>
                </{{ params.tag }}>
            {% endif %}
            {% if params.teaser %}
                <p class="item-teaser news-item__teaser">{{ params.teaser }}</p>
            {% endif %}
        </div>
        {{ Image({
            className: 'news-item__image',
            sizes: params.imageSizes,
            alt: 'image alt',
            type: 'no-image' if (range(5, 20) | random) > 15 else 'default',
            serviceID: range(50) | random
        }) }}
        {% if params.useShare %}
            <div class="news-item__actions">
                {{ Share() }}
            </div>
        {% endif %}
    </article>
{%- endmacro -%}

{% set defaultSettingsFocus = {
    title: 'Maecenas mollis, augue vitae sagittis',
    category: 'Lorem ipsum',
    imageSizes: ['479x319?479', '767x511?767', '1279x852?1279', '995x663'],
    useShare: false,
    useTitleLarge: true,
    useCategoryLarge: true
} %}

{#
    NewsFocus template.
    @param {object} settings - user settings object
#}
{%- macro NewsFocus(settings = {}) -%}
    {% set params = Helpers.merge(defaultSettingsFocus, settings) %}

    <div class="news-focus">
        <article class="news-focus__wrapper">
            <div class="news-focus__content">
                <h3 class="item-title news-focus__title {{ 'is-large' if params.useTitleLarge }}">
                    <span class="theme news-focus__category {{ 'is-large' if params.useCategoryLarge }}">{{ params.category }}</span>
                    <span class="sr-only"> : </span>
                    <a href="#" class="news-focus__title-link">
                        {% if params.useInlineBgTitle %}
                            <span class="js-inline-bg underline" data-max-length="16">{{ params.title }}</span>
                        {% else %}
                            <span class="underline">{{ params.title }}</span>
                        {% endif %}
                    </a>
                </h3>
            </div>
            <a href="#" class="news-focus__picture-link" role="paragraph" tabindex="-1">
                {{ Image({
                    sizes: params.imageSizes,
                    className: 'news-focus__picture',
                    serviceID: range(50) | random,
                    alt: 'image alt'
                }) }}
            </a>
        </article>
        {% if params.useShare %}
            <div class="news-focus__actions">
                {{ Share() }}
            </div>
        {% endif %}
    </div>
{%- endmacro -%}

{#
    NewsList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro NewsList(
    listClass = 'news-list',
    itemClass = 'news-list__item',
    count = 8,
    cols = 4,
    mdCols = 1,
    smCols = false,
    xsCols = false
) -%}
    {% call List(
        listClass = listClass,
        itemClass = itemClass,
        count = count,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols
    ) %}
        {{ NewsItem() }}
    {% endcall %}
{%- endmacro -%}

{#
    NewsContent template.
    Template for news on page-content.
    @param {string} titleText - section title
    @param {number} itemsCount - count of news
    @param {number} colsCount - count of cols
    @param {number} mdColsCount - count of cols on tablet
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{%- macro NewsContent(
    className = 'news-content',
    titleText = 'On en parle',
    itemsCount = 3,
    colsCount = 3,
    mdColsCount = false,
    moreButton = true,
    proposerButton = false,
    centeredButton = false
) -%}
    {% call Section(className = className, container = false) %}
        <div class="section__title">
            {{ Title.TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ NewsList(
                count = itemsCount,
                cols = colsCount,
                mdCols = 1,
                smCols = false,
                xsCols = false
            ) }}
        </div>
        {% if moreButton or proposerButton %}
            <div class="section__more-links {{ 'is-left' if not centeredButton }}">
                {% if proposerButton %}
                    {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un actualite',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
                {% endif %}
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listNews,
                        text = 'Toutes les actualités',
                        className = 'btn is-link is-small',
                        icon = 'far fa-long-arrow-right'
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    NewsAccount template.
    Template for news on page-content.
    @param {string} titleText - section title
    @param {number} itemsCount - count of news
    @param {number} colsCount - count of cols
    @param {number} mdColsCount - count of cols on tablet
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{%- macro NewsAccount(
    className = 'news-content',
    titleText = 'On en parle',
    itemsCount = 3,
    colsCount = 3,
    mdColsCount = false,
    moreButton = true,
    proposerButton = false
) -%}
    {% call Section(className = className, container = false) %}
        <div class="section__title">
            {{ Title.TitlePrimary(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ NewsList(
                count = itemsCount,
                cols = colsCount,
                mdCols = 1,
                smCols = false,
                xsCols = false
            ) }}
        </div>
        {% if moreButton or proposerButton %}
            <div class="section__more-links is-left">
                {% if proposerButton %}
                    {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un actualite',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
                {% endif %}
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listNews,
                        text = 'Toutes les actualités',
                        className = 'btn is-link is-small',
                        icon = 'far fa-long-arrow-right'
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    NewsHome template.
    Template for news on home page.
    @param {string} titleText - section title
    @param {number} itemsCount - count of news
    @param {boolean} moreButton - insert more link
#}
{%- macro NewsHome(
    titleText = 'Les actus',
    itemsCount = 2,
    cols = 2,
    mdCols = 2,
    listClass = 'news-list',
    listItemClass = 'news-list__item',
    sectionModifier = ''
) -%}
    {% call Section(className = 'news-home' + ' ' + sectionModifier, container = 'news-home__container') %}
        <div class="news-home__section">
            <div class="news-home__title">{{ Title.TitlePrimary(text = titleText) }}</div>
            <div class="news-home__content">{{ NewsList(count = itemsCount, cols = 2, mdCols = 1, smCols = false, xsCols = false) }}</div>
            <div class="news-home__more-links">{{ Link(href = kGlobalLinks.listNews, text = 'Toutes les actualités', className = 'btn is-primary is-sm-small', icon = "far fa-arrow-right") }}</div>
        </div>
    {% endcall %}
{%- endmacro -%}

{%- macro NewsReviewHome() -%}
    <div class="news-review-home">
        {{ NewsHome() }}
        {{ ReviewsHome() }}
    </div>
{%- endmacro -%}

{#
    NewsSidebar template.
    Template for news in sidebar.
    @param {string} titleText - section title
    @param {number} itemsCount - count of news
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{%- macro NewsSidebar(
    titleText = 'Fil infos',
    itemsCount = 1,
    moreButton = true,
    proposerButton = false
) -%}
    {% call Widget(className = 'news-widget') %}
        <div class="widget__title">
            {{ Title.TitleSidebar(
                className = 'is-center ',
                text = titleText
            ) }}
        </div>
        <div class="widget__content">
            {{ NewsList(
                count = itemsCount,
                cols = 1,
                mdCols = 3,
                smCols = 2,
                xsCols = 1
            ) }}
        </div>
        {% if moreButton or proposerButton %}
            <div class="widget__more-links">
                {% if proposerButton %}
                    {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un actualite',
                        className = 'btn is-small',
                        icon = 'far fa-calendar-plus'
                    ) }}
                {% endif %}
                {% if moreButton %}
                    {{ Link(
                        text = 'Toutes les actualites',
                        className = 'btn is-small',
                        icon = 'far fa-plus'
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}

{% set defaultSettingsShortNewsItem = {
    title: lorem(1),
    category: lorem(range(1, 3) | random, 'words'),
    tag: 'h4'
} %}

{#
    ShortNewsItem template.
    @param {object} settings - user settings object
#}
{%- macro ShortNewsItem(settings = {}) -%}
    {# Default params  #}
    {% set params = Helpers.merge(defaultSettingsShortNewsItem, settings) %}

    <article class="shortnews-item">
        {% if params.title %}
            <{{ params.tag }} class="item-teaser shortnews-item__title">
                {% if params.category %}
                    <span class="theme shortnews-item__category">{{ params.category }}</span>
                    <span class="sr-only"> : </span>
                {% endif %}
                <a href="{{ kGlobalLinks.singleNews }}" class="shortnews-item__title-link">
                    <span class="underline">{{ params.title }}</span>
                </a>
            </{{ params.tag }}>
        {% endif %}
    </article>
{%- endmacro -%}

{#
    ShortNewsList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro ShortNewsList(
    listClass = '',
    itemClass = '',
    count = 4,
    cols = 4,
    mdCols = 1,
    smCols = false,
    xsCols = 1
) -%}
    {% call List(
        listClass = listClass,
        itemClass = itemClass,
        count = count,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols
    ) %}
        {{ ShortNewsItem() }}
    {% endcall %}
{%- endmacro -%}

{#
    ShortNewsHome template.
    Template for news on home page.
    @param {string} titleText - section title
    @param {number} itemsCount - count of news
#}
{%- macro ShortNewsHome(
    titleText = 'En bref...',
    itemsCount = 4,
    cols = 4,
    mdCols = 1
) -%}
    <div class="shortnews">
        <h3 class="shortnews__title">{{ titleText }}</h3>
        {{ ShortNewsList(
            listClass = 'shortnews__list',
            itemClass = 'shortnews__list-item',
            count = itemsCount,
            cols = cols,
            mdCols = mdCols,
            smCols = false,
            xsCols = false
        ) }}
    </div>
{%- endmacro -%}
