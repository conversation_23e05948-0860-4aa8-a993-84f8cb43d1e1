.telecharger-item {
    $this: &;

    align-items: flex-start;
    background-color: $color-3--1;
    display: flex;
    padding: 25px 30px 25px 100px;
    position: relative;

    @include breakpoint(medium down, true) {
        flex-direction: column;
    }

    @include breakpoint(small down, true) {
        align-items: center;
        flex-direction: column;
        padding: 25px;
    }

    &__svg-wrap {
        @include absolute(25px, null, null, 45px);
        @include size(39px);
        display: block;
        flex-shrink: 0;

        @include breakpoint(medium down, true) {
            top: 30px;
        }

        @include breakpoint(small down, true) {
            @include reset-position;
            margin: 0 0 10px;
        }

        svg {
            @include size(100%);
            fill: var(--color-1--5);
        }
    }

    &__title {
        @include font(var(--typo-1), 1.8rem, var(--fw-medium));
        flex-grow: 1;
        line-height: calc(22 / 18);
        margin: 7px 0;
        word-break: break-word;

        @include breakpoint(medium down, true) {
            margin: 0 0 10px;
        }

        @include breakpoint(small down, true) {
            margin: 0 0 10px;
            text-align: center;
        }
    }

    &__descr {
        align-items: center;
        display: flex;
        flex-shrink: 0;

        @include breakpoint(small down, true) {
            flex-direction: column;
            text-align: center;
        }
    }

    &__meta {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $color-3--5;
        display: block;
        line-height: 1.15;
        margin: 0 20px 0 50px;
        text-transform: uppercase;

        @include breakpoint(medium down, true) {
            margin: 0;
            width: 185px;
        }

        @include breakpoint(small down, true) {
            margin: 0 0 10px;
        }
    }

    &__size {
        &::before {
            content: ' - ';
            margin: 0 2px;
        }
    }

    &__link[class] {
        border-color: var(--color-1--5);
        color: var(--color-1--5);
        margin: 2.5px;
    }
}
