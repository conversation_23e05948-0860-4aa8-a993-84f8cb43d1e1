
{#
    StratisMenu template.
#}
{%- macro StratisMenu() -%}
    {% set pages = {
        startPage: [
            'index',
            'home-video'
        ],
        pages: [
            'page-content',
            'page-form',
            'ce-gallery',
            'search',
            'search-relevant',
            'search-no-results',
            'subpages-menu',
            'sitemap',
            'page-proposer',
            'page-error',
            'page-quiz',
            'test-form',
            'maintenance'
        ],
        lists: [
            'list-news',
            'list-albums',
            'list-publications',
            'list-services',
            'list-consultations',
            'list-doctors',
            'list-faq',
            'list-job'
        ],
        single: [
            'single-news',
            'single-albums',
            'single-publications',
            'single-services',
            'single-consultations',
            'single-doctors',
            'single-job'
        ]
    } %}

    <div class="stratis-menu js-dropdown">
        <button type="button" class="stratis-menu__toggle is-outside js-dropdown-toggle">
            <span class="stratis-menu__toggle-text">Toggle stratis menu</span>
        </button>
        <nav class="stratis-menu__nav js-dropdown-block">
            <ul class="stratis-menu__list">
                {% for key, value in pages %}
                    {% if pages[key] | length > 0 %}
                        <li class="stratis-menu__item js-dropdown">
                            <a href="{{ pages[key][0] }}.html" class="stratis-menu__link">{{ key }}</a>
                            {% if pages[key] | length > 1 %}
                                <button type="button" class="stratis-menu__toggle js-dropdown-toggle">
                                    <span class="stratis-menu__toggle-icon" aria-hidden="true"></span>
                                    <span class="stratis-menu__toggle-text">Toggle menu</span>
                                </button>
                                <div class="stratis-menu__dropdown js-dropdown-block">
                                    <ul class="stratis-menu__list is-column">
                                        {% for page in pages[key] %}
                                            <li class="stratis-menu__item">
                                                <a href="{{ page }}.html" class="stratis-menu__link">{{ page }}</a>
                                            </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endif %}
                        </li>
                    {% endif %}
                {% endfor %}
            </ul>
        </nav>
    </div>
{%- endmacro -%}

