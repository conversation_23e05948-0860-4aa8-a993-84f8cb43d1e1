.site-info {
    $this: &;

    display: flex;

    @include breakpoint(medium down) {
        display: block;
    }

    &__column {
        display: flex;

        @include breakpoint(medium down) {
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        @include breakpoint(small down) {
            margin-bottom: 2px;
        }

        + #{$this}__column {
            flex-direction: column;
            flex-grow: 0;
            flex-shrink: 0;
            margin-left: 101px;
            margin-top: 15px;

            @include breakpoint(medium down) {
                flex-direction: row-reverse;
                justify-content: flex-end;
                margin: 0;
                text-align: center;
                width: 100%;
            }
        }

        &:last-of-type {
            margin-left: 153px;
            margin-top: 25px;

            @include breakpoint(medium down) {
                justify-content: center;
                margin-inline: auto;
                margin-top: 9px;
            }

            @include breakpoint(small down) {
                margin-top: 37px;
            }
        }
    }

    &__image {
        flex-shrink: 0;
        margin-right: 25px;

        @include breakpoint(medium down) {
            margin: 0 0 0 53px;
            text-align: center;
            width: fit-content;
        }

        @include breakpoint(small down) {
            margin: 12px auto 11px;
        }

        .logo {
            .is-inverted & {
                @include focus-outline($color: $color-white, $offset: 3px);
            }

            img {
                @include size(148px, 139px);

                
                @include breakpoint(medium down) {
                    @include size(142px, 133px);
                    margin: 0 auto;
                }
            }
        }
    }

    &__content {
        color: $color-black;
        flex-grow: 1;
        margin-left: 32px;
        padding-top: 18px;

        @include breakpoint(medium down) {
            margin-left: 50px;
            margin-right: auto;
            padding-top: 3px;
        }

        @include breakpoint(small down) {
            margin-inline: auto;
            padding-top: 3px;
            text-align: center;
        }

        @include add-inverted-styles {
            color: $color-white;
        }
    }

    &__item {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal), normal);
        line-height: 1.25;
        margin: 0 0 13px;

        @include fa-icon-style(false) {
            font-size: 1.8rem;
            margin-right: 5px;
        }

        a {
            @include focus-outline($offset: 2px);
            color: inherit;

            @include on-event {
                text-decoration: none;
            }
        }

        &.is-name {
            @include font(null, 1.8rem, var(--fw-bold));
            margin-bottom: 13px;
        }

        &.is-phone {
            a {
                text-decoration: none;
            }
        }
    }

    &__list {
        display: block;
        margin-bottom: 20px !important;
        
        @include breakpoint(medium down) {
            margin-left: 50px !important;
        }
        
        @include breakpoint(small down) {
            margin-bottom: 6px !important;
            margin-left: auto !important;
        }
    }

    &__list-item {
        display: inline-block;
        margin: 5px;
    }
}
