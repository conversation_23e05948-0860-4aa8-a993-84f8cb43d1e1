{%- from 'views/utils/constants.njk' import kGlobalLinks -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- import 'views/core-components/title.njk' as Title -%}

{#
    ReviewsItem template.
    @param {object} settings - user settings object
#}
{% macro ReviewsItem(
    imageSizes = ['200x200?1279', '250x250'],
    category = 'Dolor sit amet',
    quote = 'Il faut continuer d\'accélérer la mutualisation des hôpitaux de l\'Est Var',
    name = '<PERSON>',
    function = 'Directeur du CHI Brignoles Le Luc'
) %}
    <div class="reviews-item">
        <div class="reviews-item__picture-wrap">
            {{ Image({
                sizes: imageSizes,
                className: 'reviews-item__picture',
                serviceID: range(50) | random,
                alt: ''
            }) }}
        </div>
        <div class="reviews-item__content">
            {%- if category %}
                <p class="reviews-item__category">{{ category }}</p>
            {%- endif %}
            {% if quote %}
                <blockquote class="reviews-item__quote js-inline-bg" data-max-length="30">{{ quote }}</blockquote>
            {% endif %}
            <div class="reviews-item__info">
                {% if name %}
                    <p class="reviews-item__name">
                        <a href="./single-reviews.html" class="reviews-item__name-link">
                            <span class="underline">{{ name }}</span>
                        </a>
                    </p>
                {% endif %}
                {% if function %}
                    <p class="reviews-item__function">{{ function }}</p>
                {% endif %}
            </div>
        </div>
    </div>
{% endmacro %}

{#
    ReviewsList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} itemClass - item class modifier.
    @param {string} listClass - list class modifier.
#}
{%- macro ReviewsList(
    itemClass = 'has-mb-5',
    count = 12,
    cols = 1,
    mdCols = false,
    smCols = false,
    xsCols = false
) -%}
    {% call List(
        itemClass = itemClass,
        count = count,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols
    ) %}
        {{ ReviewsItem() }}
    {% endcall %}
{%- endmacro -%}

{#
    ReviewsHome template.
    Template for reviews on home page.
    @param {string} titleText - section title
    @param {number} itemsCount - count of news
    @param {boolean} moreButton - insert more link
#}
{% macro ReviewsHome(
    titleText = 'Le mot du directeur',
    itemClass = '',
    itemsCount = 1,
    cols = 1,
    moreButton = false
) %}
    {% call Section(className = 'reviews-home', container = 'reviews-home__container') %}
        <div class="section__title">
            {{ Title.TitlePrimary(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {% if itemsCount === 1 %}
                {{ ReviewsItem(
                    imageSizes = ['242x242?1279', '384x384'],
                    category = false
                ) }}
            {% else %}
                {% call List(
                    itemClass = itemClass,
                    count = itemsCount,
                    cols = cols
                ) %}
                    {{ ReviewsItem(
                        imageSizes = ['242x242?1279', '384x384'],
                        category = false
                    ) }}
                {% endcall %}
            {% endif %}
        </div>
        {% if moreButton %}
            <div class="section__more-links">
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listReviews,
                        text = 'Tous les témoignages',
                        className = 'btn is-sm-small is-primary',
                        icon = false
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{% endmacro %}
